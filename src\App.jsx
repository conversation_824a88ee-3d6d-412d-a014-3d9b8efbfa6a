import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import AgeCalculator from './tools/AgeCalculator';
import BMICalculator from './tools/BMICalculator';
import SimpleCalculator from './tools/SimpleCalculator';
import ScientificCalculator from './tools/ScientificCalculator';
import UnitConverter from './tools/UnitConverter';
import CountdownTimer from './tools/CountdownTimer';
import PasswordGenerator from './tools/PasswordGenerator';
import ColorPaletteGenerator from './tools/ColorPaletteGenerator';
import DaysUntilCalculator from './pages/DaysUntilCalculator';
import WorldClock from './pages/WorldClock';
import PercentageCalculator from './pages/PercentageCalculator';
import GradientGenerator from './pages/GradientGenerator';
// New features
import <PERSON>remIpsumGenerator from './pages/LoremIpsumGenerator';
import NumberToWordsConverter from './pages/NumberToWordsConverter';
import ResponsiveScreenTester from './pages/ResponsiveScreenTester';
import TextCounter from './pages/TextCounter';
import TextCaseConverter from './pages/TextCaseConverter';
import DateDifferenceCalculator from './pages/DateDifferenceCalculator';
// Image Tools
import ImageResizer from './pages/ImageResizer';
import ImageFormatConverter from './pages/ImageFormatConverter';
import ImageWatermarking from './pages/ImageWatermarking';
import ImageOptimizer from './pages/ImageOptimizer';
import ImageComparison from './pages/ImageComparison';
import BackgroundRemover from './pages/BackgroundRemover';
import SEO from './components/SEO';
import QRCodeGenerator from './components/qr-generator/QRCodeGenerator';
import './index.css'; // Ensure Tailwind directives are eventually imported here or in main.jsx

function App() {
  return (
    <Router>
      <SEO
        title="ToollyHub - Free Online Tools & Calculators for Everyone"
        description="ToollyHub is your one-stop solution for all calculation, conversion, and utility needs. Access 25+ free online tools including calculators, converters, generators, image tools, and more. All tools are free, accurate, and designed for everyday use."
        keywords="ToollyHub, online tools, free calculators, measurement tools, calculation tools, utility tools, web tools, free online utilities, password generator, color palette generator, unit converter, image tools, text tools, design tools, time tools"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "ToollyHub",
          "description": "Free online tools and calculators for everyone",
          "url": "https://toollyhub.com",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://toollyhub.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "ToollyHub",
            "url": "https://toollyhub.com"
          }
        }}
        additionalMeta={[
          { name: 'application-name', content: 'ToollyHub' },
          { name: 'theme-color', content: '#3B82F6' },
          { property: 'og:site_name', content: 'ToollyHub' }
        ]}
      />
      <div className="flex flex-col min-h-screen bg-gray-100"> {/* Base background for the app */}
        <Navbar />
        {/* The 'container mx-auto px-4 py-8' from example is good for specific page content, 
            but HomePage and other tool pages will manage their own top-level padding and containers.
            'flex-grow' ensures this main area pushes the footer down. */}
        <main className="flex-grow"> 
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/simple-calculator" element={<SimpleCalculator />} />
            <Route path="/scientific-calculator" element={<ScientificCalculator />} />
            <Route path="/age-calculator" element={<AgeCalculator />} />
            <Route path="/bmi-calculator" element={<BMICalculator />} />
            <Route path="/unit-converter" element={<UnitConverter />} />
            <Route path="/countdown-timer" element={<CountdownTimer />} />
            <Route path="/password-generator" element={<PasswordGenerator />} />
            <Route path="/color-palette-generator" element={<ColorPaletteGenerator />} />
            <Route path="/days-until-calculator" element={<DaysUntilCalculator />} />
            <Route path="/world-clock" element={<WorldClock />} />
            <Route path="/percentage-calculator" element={<PercentageCalculator />} />
            <Route path="/gradient-generator" element={<GradientGenerator />} />
            {/* New feature routes */}
            <Route path="/lorem-ipsum-generator" element={<LoremIpsumGenerator />} />
            <Route path="/number-to-words-converter" element={<NumberToWordsConverter />} />
            <Route path="/responsive-screen-tester" element={<ResponsiveScreenTester />} />
            <Route path="/text-counter" element={<TextCounter />} />
            <Route path="/text-case-converter" element={<TextCaseConverter />} />
            <Route path="/date-difference-calculator" element={<DateDifferenceCalculator />} />
            {/* Image Tools */}
            <Route path="/image-resizer" element={<ImageResizer />} />
            <Route path="/image-format-converter" element={<ImageFormatConverter />} />
            <Route path="/image-watermarking" element={<ImageWatermarking />} />
            <Route path="/image-optimizer" element={<ImageOptimizer />} />
            <Route path="/image-comparison" element={<ImageComparison />} />
            <Route path="/background-remover" element={<BackgroundRemover />} />
            <Route path="/qr-code-generator" element={<QRCodeGenerator />} />
            {/* Add a catch-all or 404 route later if needed */}
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
