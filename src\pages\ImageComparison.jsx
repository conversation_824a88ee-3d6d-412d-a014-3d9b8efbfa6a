import React, { useState, useRef, useEffect } from 'react';
import SEO from '../components/SEO';

const ImageComparison = () => {
  const [image1, setImage1] = useState(null);
  const [image2, setImage2] = useState(null);
  const [preview1, setPreview1] = useState(null);
  const [preview2, setPreview2] = useState(null);
  const [comparisonMode, setComparisonMode] = useState('side-by-side');
  const [sliderPosition, setSliderPosition] = useState(50);
  const [showDifferences, setShowDifferences] = useState(false);
  const [imageInfo1, setImageInfo1] = useState(null);
  const [imageInfo2, setImageInfo2] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  
  const fileInput1Ref = useRef(null);
  const fileInput2Ref = useRef(null);
  const canvasRef = useRef(null);
  const containerRef = useRef(null);

  // Comparison modes
  const comparisonModes = [
    { value: 'side-by-side', label: 'Side by Side', description: 'View images next to each other' },
    { value: 'overlay', label: 'Overlay', description: 'Overlay images with adjustable opacity' },
    { value: 'slider', label: 'Slider', description: 'Interactive slider comparison' },
    { value: 'difference', label: 'Difference', description: 'Highlight differences between images' }
  ];

  // Handle image selection
  const handleImageSelect = (event, imageNumber) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const imageInfo = {
            name: file.name,
            size: file.size,
            width: img.width,
            height: img.height,
            type: file.type
          };

          if (imageNumber === 1) {
            setImage1(file);
            setPreview1(e.target.result);
            setImageInfo1(imageInfo);
          } else {
            setImage2(file);
            setPreview2(e.target.result);
            setImageInfo2(imageInfo);
          }
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    } else {
      alert('Please select a valid image file.');
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Analyze images
  const analyzeImages = () => {
    if (!imageInfo1 || !imageInfo2) return;
    
    setIsAnalyzing(true);
    
    // Simulate analysis (in a real app, this would be more sophisticated)
    setTimeout(() => {
      const results = {
        dimensionMatch: imageInfo1.width === imageInfo2.width && imageInfo1.height === imageInfo2.height,
        sizeComparison: {
          image1: formatFileSize(imageInfo1.size),
          image2: formatFileSize(imageInfo2.size),
          difference: Math.abs(imageInfo1.size - imageInfo2.size),
          percentageDiff: Math.round(Math.abs(imageInfo1.size - imageInfo2.size) / Math.max(imageInfo1.size, imageInfo2.size) * 100)
        },
        aspectRatio: {
          image1: (imageInfo1.width / imageInfo1.height).toFixed(2),
          image2: (imageInfo2.width / imageInfo2.height).toFixed(2),
          match: Math.abs((imageInfo1.width / imageInfo1.height) - (imageInfo2.width / imageInfo2.height)) < 0.01
        },
        formatMatch: imageInfo1.type === imageInfo2.type
      };
      
      setAnalysisResults(results);
      setIsAnalyzing(false);
    }, 1000);
  };

  // Reset tool
  const resetTool = () => {
    setImage1(null);
    setImage2(null);
    setPreview1(null);
    setPreview2(null);
    setImageInfo1(null);
    setImageInfo2(null);
    setAnalysisResults(null);
    setComparisonMode('side-by-side');
    setSliderPosition(50);
    setShowDifferences(false);
    if (fileInput1Ref.current) fileInput1Ref.current.value = '';
    if (fileInput2Ref.current) fileInput2Ref.current.value = '';
  };

  // Handle slider drag for slider comparison mode
  const handleSliderDrag = (e) => {
    if (comparisonMode !== 'slider' || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  };

  // Handle mouse events for slider
  useEffect(() => {
    let isDragging = false;

    const handleMouseDown = (e) => {
      if (comparisonMode === 'slider' && containerRef.current) {
        isDragging = true;
        handleSliderDrag(e);
      }
    };

    const handleMouseMove = (e) => {
      if (isDragging) {
        handleSliderDrag(e);
      }
    };

    const handleMouseUp = () => {
      isDragging = false;
    };

    if (containerRef.current) {
      const container = containerRef.current;
      container.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        container.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [comparisonMode]);

  // Auto-analyze when both images are loaded
  useEffect(() => {
    if (imageInfo1 && imageInfo2) {
      analyzeImages();
    }
  }, [imageInfo1, imageInfo2]);

  return (
    <>
      <SEO
        title="Image Comparison Tool - Compare Images Side by Side Online | ToollyHub"
        description="Compare two images side by side with advanced comparison modes including overlay, slider, and difference detection. Perfect for before/after comparisons, quality analysis, and visual inspection. Support for multiple image formats with detailed analysis features."
        keywords="image comparison, compare images, image diff, side by side comparison, image analysis, visual comparison, before after comparison, image difference, photo comparison, image inspector, visual diff, image quality comparison, photo analysis, image review, comparison tool, image validator, visual inspection, free image comparison, online comparison tool"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Image Comparison Tool",
          "description": "Compare two images side by side with advanced comparison modes",
          "url": "https://toollyhub.com/image-comparison",
          "applicationCategory": "MultimediaApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Side by side comparison",
            "Overlay comparison mode",
            "Slider comparison",
            "Difference detection",
            "Image analysis",
            "Multiple format support",
            "Zoom functionality",
            "Detailed comparison metrics"
          ],
          "audience": {
            "@type": "Audience",
            "audienceType": ["Photographers", "Quality Analysts", "Designers", "Developers"]
          }
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Image Comparison Tool' },
          { property: 'og:image:alt', content: 'Image Comparison Tool - Compare Images Side by Side Online' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">Image Comparison Tool</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Compare two images side by side with advanced analysis
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Controls Section */}
            <div className="lg:col-span-1 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6">
                <h2 className="text-xl font-bold text-white">Comparison Settings</h2>
                <p className="text-blue-100 mt-1">Configure comparison options</p>
              </div>
              <div className="p-6 space-y-6">
                
                {/* Image Upload 1 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">First Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                    <input
                      ref={fileInput1Ref}
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageSelect(e, 1)}
                      className="hidden"
                      id="imageInput1"
                    />
                    <label htmlFor="imageInput1" className="cursor-pointer">
                      {preview1 ? (
                        <img src={preview1} alt="Image 1" className="max-w-full h-20 mx-auto rounded object-cover" />
                      ) : (
                        <div className="space-y-2">
                          <div className="text-3xl">🖼️</div>
                          <p className="text-gray-600 text-sm">Click to select</p>
                        </div>
                      )}
                    </label>
                  </div>
                </div>

                {/* Image Upload 2 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Second Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                    <input
                      ref={fileInput2Ref}
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageSelect(e, 2)}
                      className="hidden"
                      id="imageInput2"
                    />
                    <label htmlFor="imageInput2" className="cursor-pointer">
                      {preview2 ? (
                        <img src={preview2} alt="Image 2" className="max-w-full h-20 mx-auto rounded object-cover" />
                      ) : (
                        <div className="space-y-2">
                          <div className="text-3xl">🖼️</div>
                          <p className="text-gray-600 text-sm">Click to select</p>
                        </div>
                      )}
                    </label>
                  </div>
                </div>

                {/* Comparison Mode */}
                {preview1 && preview2 && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Comparison Mode</label>
                      <div className="space-y-2">
                        {comparisonModes.map((mode) => (
                          <button
                            key={mode.value}
                            className={`w-full p-3 rounded-lg border text-left transition-all duration-200 ${
                              comparisonMode === mode.value
                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                : 'border-gray-200 hover:border-blue-300 text-gray-700'
                            }`}
                            onClick={() => setComparisonMode(mode.value)}
                          >
                            <div className="font-medium">{mode.label}</div>
                            <div className="text-sm text-gray-500">{mode.description}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Slider Control for Overlay and Slider modes */}
                    {(comparisonMode === 'overlay' || comparisonMode === 'slider') && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {comparisonMode === 'overlay' ? 'Opacity' : 'Position'}: {sliderPosition}%
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={sliderPosition}
                          onChange={(e) => setSliderPosition(parseInt(e.target.value))}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>{comparisonMode === 'overlay' ? 'Image 1' : 'Left'}</span>
                          <span>{comparisonMode === 'overlay' ? 'Image 2' : 'Right'}</span>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={analyzeImages}
                        disabled={isAnalyzing}
                        className="flex-1 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 shadow-md disabled:opacity-50 text-sm"
                      >
                        {isAnalyzing ? 'Analyzing...' : 'Analyze'}
                      </button>
                      <button
                        onClick={resetTool}
                        className="px-4 py-2 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 shadow-md text-sm"
                      >
                        Reset
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Comparison Display */}
            <div className="lg:col-span-3 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6">
                <h2 className="text-xl font-bold text-white">Image Comparison</h2>
                <p className="text-orange-100 mt-1">Visual comparison and analysis</p>
              </div>
              <div className="p-6">
                {preview1 && preview2 ? (
                  <div className="space-y-6">
                    {/* Comparison View */}
                    <div className="relative">
                      {comparisonMode === 'side-by-side' && (
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <h3 className="text-sm font-medium text-gray-700">Image 1</h3>
                            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                              <img
                                src={preview1}
                                alt="Image 1"
                                className="max-w-full h-auto max-h-80 mx-auto rounded-lg shadow-sm"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <h3 className="text-sm font-medium text-gray-700">Image 2</h3>
                            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                              <img
                                src={preview2}
                                alt="Image 2"
                                className="max-w-full h-auto max-h-80 mx-auto rounded-lg shadow-sm"
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {comparisonMode === 'overlay' && (
                        <div className="relative border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <div className="relative max-w-full max-h-96 mx-auto">
                            <img
                              src={preview1}
                              alt="Image 1"
                              className="max-w-full h-auto rounded-lg shadow-sm"
                            />
                            <img
                              src={preview2}
                              alt="Image 2"
                              className="absolute top-0 left-0 max-w-full h-auto rounded-lg shadow-sm"
                              style={{ opacity: sliderPosition / 100 }}
                            />
                          </div>
                          <div className="mt-2 text-center text-sm text-gray-600">
                            Opacity: {sliderPosition}% (Image 2 over Image 1)
                          </div>
                        </div>
                      )}

                      {comparisonMode === 'slider' && (
                        <div className="relative border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <div
                            ref={containerRef}
                            className="relative max-w-full max-h-96 mx-auto overflow-hidden rounded-lg"
                          >
                            <img
                              src={preview1}
                              alt="Image 1"
                              className="max-w-full h-auto block"
                            />
                            <div
                              className="absolute top-0 left-0 h-full overflow-hidden"
                              style={{ width: `${sliderPosition}%` }}
                            >
                              <img
                                src={preview2}
                                alt="Image 2"
                                className="max-w-none h-full object-cover"
                              />
                            </div>
                            <div
                              className="absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-ew-resize"
                              style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
                            >
                              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white rounded-full shadow-lg flex items-center justify-center">
                                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-2 text-center text-sm text-gray-600">
                            Drag the slider to compare • Position: {sliderPosition}%
                          </div>
                        </div>
                      )}

                      {comparisonMode === 'difference' && (
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <div className="text-center py-8">
                            <div className="text-4xl mb-4">🔍</div>
                            <p className="text-gray-600">Difference detection coming soon</p>
                            <p className="text-sm text-gray-400 mt-2">This feature will highlight pixel differences between images</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Analysis Results */}
                    {analysisResults && (
                      <div className="bg-blue-50 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Analysis Results</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-3">
                            <h4 className="font-medium text-gray-700">Image Properties</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Dimensions Match:</span>
                                <span className={analysisResults.dimensionMatch ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                                  {analysisResults.dimensionMatch ? '✓ Yes' : '✗ No'}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Format Match:</span>
                                <span className={analysisResults.formatMatch ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                                  {analysisResults.formatMatch ? '✓ Yes' : '✗ No'}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Aspect Ratio Match:</span>
                                <span className={analysisResults.aspectRatio.match ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                                  {analysisResults.aspectRatio.match ? '✓ Yes' : '✗ No'}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <h4 className="font-medium text-gray-700">Size Comparison</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Image 1 Size:</span>
                                <span className="font-medium">{analysisResults.sizeComparison.image1}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Image 2 Size:</span>
                                <span className="font-medium">{analysisResults.sizeComparison.image2}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Size Difference:</span>
                                <span className="font-medium">{analysisResults.sizeComparison.percentageDiff}%</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Detailed Info */}
                        <div className="mt-6 pt-4 border-t border-blue-200">
                          <h4 className="font-medium text-gray-700 mb-3">Detailed Information</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="bg-white p-3 rounded">
                              <h5 className="font-medium text-gray-600 mb-2">Image 1</h5>
                              <p><strong>Name:</strong> {imageInfo1.name}</p>
                              <p><strong>Dimensions:</strong> {imageInfo1.width} × {imageInfo1.height} px</p>
                              <p><strong>Aspect Ratio:</strong> {analysisResults.aspectRatio.image1}</p>
                              <p><strong>Type:</strong> {imageInfo1.type}</p>
                            </div>
                            <div className="bg-white p-3 rounded">
                              <h5 className="font-medium text-gray-600 mb-2">Image 2</h5>
                              <p><strong>Name:</strong> {imageInfo2.name}</p>
                              <p><strong>Dimensions:</strong> {imageInfo2.width} × {imageInfo2.height} px</p>
                              <p><strong>Aspect Ratio:</strong> {analysisResults.aspectRatio.image2}</p>
                              <p><strong>Type:</strong> {imageInfo2.type}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🔍</div>
                    <p className="text-gray-500 text-lg">Upload two images to start comparison</p>
                    <p className="text-gray-400 text-sm mt-2">Compare images side by side with advanced analysis tools</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Hidden canvas for image processing */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">About Image Comparison</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Comparison Features</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Compare images with multiple viewing modes to analyze differences, similarities,
                  and technical specifications between two images.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <strong>Side by Side:</strong> Traditional comparison view
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    <strong>Overlay Mode:</strong> Blend images with adjustable opacity
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    <strong>Slider View:</strong> Interactive before/after comparison
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Analysis Tools</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    Automatic dimension and format analysis
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    File size and compression comparison
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    Aspect ratio and technical specifications
                  </li>
                  <li className="flex items-center">
                    <span className="text-teal-500 mr-2">•</span>
                    Visual difference detection (coming soon)
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageComparison;
