import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const ImageResizer = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [resizeMethod, setResizeMethod] = useState('dimensions'); // 'dimensions' or 'percentage'
  const [dimensions, setDimensions] = useState({ width: '', height: '' });
  const [percentage, setPercentage] = useState('');
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 });
  const [resizedImage, setResizedImage] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setOriginalDimensions({ width: img.width, height: img.height });
          setDimensions({ width: img.width.toString(), height: img.height.toString() });
        };
        img.src = e.target.result;
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
      setResizedImage(null);
    } else {
      alert('Please select a valid image file.');
    }
  };

  // Handle dimension change with aspect ratio maintenance
  const handleDimensionChange = (field, value) => {
    if (!maintainAspectRatio) {
      setDimensions(prev => ({ ...prev, [field]: value }));
      return;
    }

    const numValue = parseInt(value) || 0;
    if (field === 'width') {
      const newHeight = Math.round((numValue * originalDimensions.height) / originalDimensions.width);
      setDimensions({ width: value, height: newHeight.toString() });
    } else {
      const newWidth = Math.round((numValue * originalDimensions.width) / originalDimensions.height);
      setDimensions({ width: newWidth.toString(), height: value });
    }
  };

  // Resize image
  const resizeImage = () => {
    if (!selectedImage || !imagePreview) return;

    setIsProcessing(true);
    
    const img = new Image();
    img.onload = () => {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      let newWidth, newHeight;
      
      if (resizeMethod === 'percentage') {
        const scale = parseFloat(percentage) / 100;
        newWidth = Math.round(originalDimensions.width * scale);
        newHeight = Math.round(originalDimensions.height * scale);
      } else {
        newWidth = parseInt(dimensions.width) || originalDimensions.width;
        newHeight = parseInt(dimensions.height) || originalDimensions.height;
      }
      
      canvas.width = newWidth;
      canvas.height = newHeight;
      
      // Draw resized image
      ctx.drawImage(img, 0, 0, newWidth, newHeight);
      
      // Convert to blob and create download URL
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        setResizedImage(url);
        setIsProcessing(false);
      }, selectedImage.type, 0.9);
    };
    
    img.src = imagePreview;
  };

  // Download resized image
  const downloadImage = () => {
    if (!resizedImage) return;
    
    const link = document.createElement('a');
    link.href = resizedImage;
    link.download = `resized_${selectedImage.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Reset all states
  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setResizedImage(null);
    setDimensions({ width: '', height: '' });
    setPercentage('');
    setOriginalDimensions({ width: 0, height: 0 });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <SEO
        title="Image Resizer - Resize Images Online Free by Dimensions or Percentage | ToollyHub"
        description="Resize your images easily by specifying custom dimensions or percentage. Maintain aspect ratio and download high-quality resized images instantly. Support for JPEG, PNG, WebP, and GIF formats. Perfect for web optimization, social media, printing, and email attachments."
        keywords="image resizer, resize image, image dimensions, image percentage resize, photo resizer, image tool, resize photos online, image size reducer, photo dimensions, image scaling, picture resizer, image compression, web image optimization, social media image resize, batch image resize, maintain aspect ratio, image quality, free image resizer, online image tool, photo editor"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Image Resizer",
          "description": "Resize images by custom dimensions or percentage while maintaining quality",
          "url": "https://toollyhub.com/image-resizer",
          "applicationCategory": "MultimediaApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Resize by custom dimensions",
            "Resize by percentage",
            "Maintain aspect ratio",
            "Multiple image formats support",
            "High-quality output",
            "Instant download",
            "Preview before download",
            "No file size limits"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Image Resizer' },
          { property: 'og:image:alt', content: 'Image Resizer - Resize Images Online Free by Dimensions or Percentage' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">Image Resizer</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Resize images by dimensions or percentage
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Controls Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6">
                <h2 className="text-xl font-bold text-white">Upload & Settings</h2>
                <p className="text-indigo-100 mt-1">Configure your image resize options</p>
              </div>
              <div className="p-6 space-y-6">
                
                {/* File Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="imageInput"
                    />
                    <label htmlFor="imageInput" className="cursor-pointer">
                      <div className="space-y-2">
                        <div className="text-4xl">📁</div>
                        <p className="text-gray-600">Click to select an image</p>
                        <p className="text-sm text-gray-400">Supports JPG, PNG, GIF, WebP</p>
                      </div>
                    </label>
                  </div>
                </div>

                {selectedImage && (
                  <>
                    {/* Original Image Info */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-medium text-gray-800 mb-2">Original Image</h3>
                      <p className="text-sm text-gray-600">
                        <strong>File:</strong> {selectedImage.name}
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Size:</strong> {originalDimensions.width} × {originalDimensions.height} pixels
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>File Size:</strong> {(selectedImage.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>

                    {/* Resize Method */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Resize Method</label>
                      <div className="flex space-x-3">
                        <button
                          className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                            resizeMethod === 'dimensions' 
                              ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-md transform scale-105' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                          onClick={() => setResizeMethod('dimensions')}
                        >
                          By Dimensions
                        </button>
                        <button
                          className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                            resizeMethod === 'percentage' 
                              ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-md transform scale-105' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                          onClick={() => setResizeMethod('percentage')}
                        >
                          By Percentage
                        </button>
                      </div>
                    </div>

                    {/* Resize Options */}
                    {resizeMethod === 'dimensions' ? (
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="aspectRatio"
                            checked={maintainAspectRatio}
                            onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          />
                          <label htmlFor="aspectRatio" className="text-sm text-gray-700">
                            Maintain aspect ratio
                          </label>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Width (px)</label>
                            <input
                              type="number"
                              value={dimensions.width}
                              onChange={(e) => handleDimensionChange('width', e.target.value)}
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Width"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Height (px)</label>
                            <input
                              type="number"
                              value={dimensions.height}
                              onChange={(e) => handleDimensionChange('height', e.target.value)}
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Height"
                            />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Resize Percentage</label>
                        <input
                          type="number"
                          value={percentage}
                          onChange={(e) => setPercentage(e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="e.g., 50 for 50%"
                          min="1"
                          max="500"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          Enter percentage (e.g., 50 for 50% of original size)
                        </p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={resizeImage}
                        disabled={isProcessing || (resizeMethod === 'percentage' && !percentage) || 
                                 (resizeMethod === 'dimensions' && (!dimensions.width || !dimensions.height))}
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? 'Processing...' : 'Resize Image'}
                      </button>
                      <button
                        onClick={resetTool}
                        className="px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md"
                      >
                        Reset
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Preview Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-green-500 to-teal-500 p-6">
                <h2 className="text-xl font-bold text-white">Preview</h2>
                <p className="text-green-100 mt-1">Original and resized image preview</p>
              </div>
              <div className="p-6">
                {imagePreview ? (
                  <div className="space-y-6">
                    {/* Original Image */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-3">Original Image</h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <img
                          src={imagePreview}
                          alt="Original"
                          className="max-w-full h-auto max-h-48 mx-auto rounded-lg shadow-sm"
                        />
                      </div>
                    </div>

                    {/* Resized Image */}
                    {resizedImage && (
                      <div>
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="text-lg font-semibold text-gray-800">Resized Image</h3>
                          <button
                            onClick={downloadImage}
                            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-md text-sm"
                          >
                            📥 Download
                          </button>
                        </div>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <img
                            src={resizedImage}
                            alt="Resized"
                            className="max-w-full h-auto max-h-48 mx-auto rounded-lg shadow-sm"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🖼️</div>
                    <p className="text-gray-500 text-lg">Upload an image to see preview</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Hidden canvas for image processing */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">About Image Resizing</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Resize Methods</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Our image resizer offers two convenient methods to resize your images: by specific dimensions or by percentage.
                  Both methods maintain image quality while giving you precise control over the output size.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <strong>By Dimensions:</strong> Set exact width and height in pixels
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    <strong>By Percentage:</strong> Scale image proportionally
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Key Features</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    Maintain aspect ratio automatically
                  </li>
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    Support for JPG, PNG, GIF, WebP formats
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    High-quality image processing
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    Instant download of resized images
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageResizer;
