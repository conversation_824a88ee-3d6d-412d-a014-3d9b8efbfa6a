import React from 'react';
import ToolCard from '../components/ToolCard'; // Adjust path if necessary
import SEO from '../components/SEO';

const HomePage = () => {
  const toolsData = [
    {
      title: "Simple Calculator",
      description: "Perform basic arithmetic operations with our easy-to-use calculator.",
      icon: "🧮",
      linkTo: "/simple-calculator",
    },
    {
      title: "Scientific Calculator",
      description: "Advanced calculator with trigonometric, logarithmic, and other scientific functions.",
      icon: "🔬",
      linkTo: "/scientific-calculator",
    },
    {
      title: "Age Calculator",
      description: "Calculate your age precisely in years, months, and days.",
      icon: "🎂",
      linkTo: "/age-calculator",
    },
    {
      title: "BMI Calculator",
      description: "Check your Body Mass Index with category and health tips.",
      icon: "⚖️",
      linkTo: "/bmi-calculator",
    },
    {
      title: "Unit Converter",
      description: "Convert various units of length, weight, and temperature.",
      icon: "🔄",
      linkTo: "/unit-converter",
    },
    {
      title: "Countdown Timer",
      description: "Set a timer for future events and watch the countdown.",
      icon: "⏱️",
      linkTo: "/countdown-timer",
    },
    {
      title: "Password Generator",
      description: "Generate secure passwords and check password strength.",
      icon: "🔐",
      linkTo: "/password-generator",
    },
    {
      title: "Color Palette Generator",
      description: "Create beautiful color palettes, lock colors, and share them with others.",
      icon: "🎨",
      linkTo: "/color-palette-generator",
    },
    {
      title: "Days Until Calculator",
      description: "Find out how many days until important events like Christmas or any custom date.",
      icon: "📅",
      linkTo: "/days-until-calculator",
    },
    {
      title: "World Clock",
      description: "Check current time in major cities around the world with this timezone tool.",
      icon: "🕓",
      linkTo: "/world-clock",
    },
    {
      title: "Percentage Calculator",
      description: "Calculate percentages, discounts, increases, and decreases quickly and easily.",
      icon: "🧮",
      linkTo: "/percentage-calculator",
    },
    {
      title: "Gradient Generator",
      description: "Create beautiful CSS gradients for your website with customizable colors and directions.",
      icon: "🌈",
      linkTo: "/gradient-generator",
    },
    {
      title: "Lorem Ipsum Generator",
      description: "Generate placeholder text for web design, typography, and layout projects.",
      icon: "📝",
      linkTo: "/lorem-ipsum-generator",
    },
    {
      title: "Number to Words Converter",
      description: "Convert numbers to their word representation in English, perfect for financial documents.",
      icon: "🔢",
      linkTo: "/number-to-words-converter",
    },
    {
      title: "Responsive Screen Tester",
      description: "Test how your website looks on different screen sizes and devices.",
      icon: "📱",
      linkTo: "/responsive-screen-tester",
    },
    {
      title: "Text Counter",
      description: "Count characters, words, sentences, and paragraphs in your text with detailed statistics.",
      icon: "📊",
      linkTo: "/text-counter",
    },
    {
      title: "Text Case Converter",
      description: "Convert text between different cases: uppercase, lowercase, title case, and more.",
      icon: "🔡",
      linkTo: "/text-case-converter",
    },
    {
      title: "Date Difference Calculator",
      description: "Calculate the exact difference between two dates in various time units.",
      icon: "📆",
      linkTo: "/date-difference-calculator",
    },
    {
      title: "Image Resizer",
      description: "Resize images by specifying custom dimensions or percentage while maintaining quality.",
      icon: "🖼️",
      linkTo: "/image-resizer",
    },
    {
      title: "Image Format Converter",
      description: "Convert images between different formats like JPEG, PNG, WebP, GIF with quality control.",
      icon: "🔄",
      linkTo: "/image-format-converter",
    },
    {
      title: "Image Watermarking Tool",
      description: "Add custom text or logo watermarks to protect and brand your images with full control.",
      icon: "🏷️",
      linkTo: "/image-watermarking",
    },
    {
      title: "Image Optimizer",
      description: "Intelligently optimize images for web use with compression, resizing, and format conversion.",
      icon: "⚡",
      linkTo: "/image-optimizer",
    },
    {
      title: "Image Comparison Tool",
      description: "Compare two images side by side with advanced comparison features and analysis.",
      icon: "🔍",
      linkTo: "/image-comparison",
    },
    // Future tools can be added here
  ];

  // Generate comprehensive keywords from all tools
  const allToolKeywords = toolsData.map(tool => {
    const toolName = tool.title.toLowerCase();
    const baseKeywords = toolName.split(' ');
    return [...baseKeywords, `${toolName} online`, `free ${toolName}`, `${toolName} tool`];
  }).flat().join(', ');

  // Create structured data for the website
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "ToollyHub",
    "description": "Free online tools for calculations, conversions, and everyday tasks",
    "url": "https://toollyhub.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://toollyhub.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": toolsData.map((tool, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "WebApplication",
          "name": tool.title,
          "description": tool.description,
          "url": `https://toollyhub.com${tool.linkTo}`,
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          }
        }
      }))
    }
  };

  return (
    <>
      <SEO
        title="ToollyHub - Free Online Tools & Calculators for Everyone"
        description="ToollyHub offers 25+ free online tools including calculators (BMI, Age, Scientific), converters (Unit, Text Case, Number to Words), generators (Password, QR Code, Lorem Ipsum), image tools (Resizer, Format Converter, Optimizer), and more. All tools are free, accurate, and easy to use."
        keywords={`online tools, free calculators, web tools, utility tools, ${allToolKeywords}, BMI calculator, age calculator, unit converter, countdown timer, password generator, color palette generator, gradient generator, lorem ipsum generator, number to words converter, text case converter, text counter, responsive screen tester, date difference calculator, days until calculator, world clock, percentage calculator, image resizer, image format converter, image watermarking, image optimizer, image comparison, background remover, QR code generator, scientific calculator, simple calculator`}
        structuredData={structuredData}
        additionalMeta={[
          { name: 'application-name', content: 'ToollyHub' },
          { name: 'theme-color', content: '#3B82F6' },
          { name: 'msapplication-TileColor', content: '#3B82F6' },
          { property: 'og:image:width', content: '1200' },
          { property: 'og:image:height', content: '630' },
          { property: 'og:image:alt', content: 'ToollyHub - Free Online Tools' }
        ]}
      />
      <div className="container mx-auto p-6 min-h-screen"> {/* Added min-h-screen for better footer placement */}
        <header className="text-center my-10">
          <h1 className="text-5xl font-bold text-gray-800">
            Welcome to ToollyHub!
          </h1>
          <p className="text-xl text-gray-600 mt-4">
            Your go-to hub for smart, everyday tools.
          </p>
        </header>

        {/* Ad Placeholder 1 - After Header */}
        <div className="my-12 p-4 h-24 border-2 border-dashed border-gray-300 bg-gray-50 text-center text-gray-500 flex items-center justify-center shadow-sm rounded-md">
          <span className="text-lg">Ad Placeholder (e.g., Leaderboard 728x90)</span>
        </div>

        <main>
          <h2 className="text-3xl font-semibold text-gray-700 mb-8 text-center md:text-left">
            Our Tools
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {toolsData.map((tool) => (
              <ToolCard
                key={tool.title}
                title={tool.title}
                description={tool.description}
                icon={tool.icon}
                linkTo={tool.linkTo}
              />
            ))}
          </div>
        </main>

        {/* Ad Placeholder 2 - Potentially after tools or before footer */}
        <div className="my-16 p-4 h-48 border-2 border-dashed border-gray-300 bg-gray-50 text-center text-gray-500 flex items-center justify-center shadow-sm rounded-md">
          <span className="text-lg">Ad Placeholder (e.g., Medium Rectangle 300x250)</span>
        </div>
      </div>
    </>
  );
};

export default HomePage;
