import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const ImageOptimizer = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [optimizedImage, setOptimizedImage] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const [originalSize, setOriginalSize] = useState(0);
  const [optimizedSize, setOptimizedSize] = useState(0);
  
  // Optimization settings
  const [quality, setQuality] = useState(80);
  const [outputFormat, setOutputFormat] = useState('auto');
  const [maxWidth, setMaxWidth] = useState(1920);
  const [maxHeight, setMaxHeight] = useState(1080);
  const [enableResize, setEnableResize] = useState(false);
  const [compressionLevel, setCompressionLevel] = useState('balanced');
  
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  // Compression presets
  const compressionPresets = {
    maximum: { quality: 60, description: 'Maximum compression, smaller file size' },
    balanced: { quality: 80, description: 'Balanced quality and file size' },
    quality: { quality: 95, description: 'High quality, larger file size' }
  };

  // Output format options
  const formatOptions = [
    { value: 'auto', label: 'Auto (Smart Choice)', description: 'Automatically choose the best format' },
    { value: 'jpeg', label: 'JPEG', description: 'Best for photos with many colors' },
    { value: 'png', label: 'PNG', description: 'Best for images with transparency' },
    { value: 'webp', label: 'WebP', description: 'Modern format with excellent compression' }
  ];

  // Handle image selection
  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      setOriginalSize(file.size);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setImageDimensions({ width: img.width, height: img.height });
        };
        img.src = e.target.result;
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
      setOptimizedImage(null);
      setOptimizedSize(0);
    } else {
      alert('Please select a valid image file.');
    }
  };

  // Smart format detection
  const getOptimalFormat = (originalFormat, hasTransparency = false) => {
    if (outputFormat !== 'auto') return outputFormat;
    
    // If image has transparency, prefer PNG or WebP
    if (hasTransparency) {
      return 'png';
    }
    
    // For photos, prefer JPEG or WebP
    if (originalFormat === 'image/jpeg' || originalFormat === 'image/jpg') {
      return 'jpeg';
    }
    
    // Default to WebP for best compression
    return 'webp';
  };

  // Check if image has transparency
  const hasTransparency = (canvas, ctx) => {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] < 255) {
        return true;
      }
    }
    return false;
  };

  // Optimize image
  const optimizeImage = () => {
    if (!selectedImage || !imagePreview) return;
    
    setIsProcessing(true);
    
    const img = new Image();
    img.onload = () => {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      let { width, height } = img;
      
      // Resize if enabled and dimensions exceed limits
      if (enableResize) {
        const aspectRatio = width / height;
        
        if (width > maxWidth) {
          width = maxWidth;
          height = width / aspectRatio;
        }
        
        if (height > maxHeight) {
          height = maxHeight;
          width = height * aspectRatio;
        }
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw image with high quality
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
      ctx.drawImage(img, 0, 0, width, height);
      
      // Check for transparency
      const hasAlpha = hasTransparency(canvas, ctx);
      
      // Determine optimal format
      const optimalFormat = getOptimalFormat(selectedImage.type, hasAlpha);
      
      // Set quality based on compression level
      const currentQuality = compressionPresets[compressionLevel].quality / 100;
      
      // Convert to blob
      const mimeType = optimalFormat === 'jpeg' ? 'image/jpeg' : 
                      optimalFormat === 'png' ? 'image/png' : 
                      'image/webp';
      
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setOptimizedImage(url);
          setOptimizedSize(blob.size);
        } else {
          alert('Failed to optimize image. Please try again.');
        }
        setIsProcessing(false);
      }, mimeType, currentQuality);
    };
    
    img.onerror = () => {
      alert('Failed to load the image. Please try again.');
      setIsProcessing(false);
    };
    
    img.src = imagePreview;
  };

  // Download optimized image
  const downloadImage = () => {
    if (!optimizedImage) return;
    
    const link = document.createElement('a');
    link.href = optimizedImage;
    
    // Generate filename with optimization info
    const originalName = selectedImage.name.split('.')[0];
    const format = getOptimalFormat(selectedImage.type);
    const extension = format === 'jpeg' ? 'jpg' : format;
    const compressionInfo = compressionLevel;
    
    link.download = `${originalName}_optimized_${compressionInfo}.${extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Calculate compression ratio
  const getCompressionRatio = () => {
    if (originalSize === 0 || optimizedSize === 0) return 0;
    return Math.round(((originalSize - optimizedSize) / originalSize) * 100);
  };

  // Reset tool
  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setOptimizedImage(null);
    setOriginalSize(0);
    setOptimizedSize(0);
    setImageDimensions({ width: 0, height: 0 });
    setQuality(80);
    setOutputFormat('auto');
    setMaxWidth(1920);
    setMaxHeight(1080);
    setEnableResize(false);
    setCompressionLevel('balanced');
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  return (
    <>
      <SEO
        title="Image Optimizer - Compress & Optimize Images for Web Performance | ToollyHub"
        description="Intelligently optimize images for web use with smart compression, resizing, and format conversion. Reduce file sizes while maintaining quality. Perfect for website performance, SEO, and faster loading times. Support for JPEG, PNG, WebP optimization with advanced algorithms."
        keywords="image optimizer, image compression, web optimization, image resizer, file size reducer, image quality, webp converter, photo optimizer, image compressor, web performance, SEO optimization, page speed, image size reduction, smart compression, lossless compression, lossy compression, website optimization, image loading speed, free image optimizer, online image compression"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Image Optimizer",
          "description": "Intelligently optimize images for web performance with smart compression",
          "url": "https://toollyhub.com/image-optimizer",
          "applicationCategory": "MultimediaApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Smart image compression",
            "Quality preservation",
            "File size reduction",
            "Format optimization",
            "Web performance enhancement",
            "Multiple compression levels",
            "Before/after comparison",
            "Instant optimization"
          ],
          "audience": {
            "@type": "Audience",
            "audienceType": ["Web Developers", "SEO Specialists", "Website Owners", "Digital Marketers"]
          }
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Image Optimizer' },
          { property: 'og:image:alt', content: 'Image Optimizer - Compress & Optimize Images for Web Performance' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">Image Optimizer</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Intelligently optimize images for web use
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Controls Section */}
            <div className="lg:col-span-1 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-green-500 to-teal-600 p-6">
                <h2 className="text-xl font-bold text-white">Optimization Settings</h2>
                <p className="text-green-100 mt-1">Configure optimization parameters</p>
              </div>
              <div className="p-6 space-y-6">
                
                {/* Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageSelect}
                      className="hidden"
                      id="imageInput"
                    />
                    <label htmlFor="imageInput" className="cursor-pointer">
                      <div className="space-y-2">
                        <div className="text-4xl">⚡</div>
                        <p className="text-gray-600">Click to select image</p>
                        <p className="text-sm text-gray-400">JPG, PNG, GIF, WebP</p>
                      </div>
                    </label>
                  </div>
                </div>

                {selectedImage && (
                  <>
                    {/* Image Info */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-medium text-gray-800 mb-2">Original Image</h3>
                      <p className="text-sm text-gray-600">
                        <strong>File:</strong> {selectedImage.name}
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Size:</strong> {imageDimensions.width} × {imageDimensions.height} px
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>File Size:</strong> {formatFileSize(originalSize)}
                      </p>
                    </div>

                    {/* Compression Level */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Compression Level</label>
                      <div className="space-y-2">
                        {Object.entries(compressionPresets).map(([key, preset]) => (
                          <button
                            key={key}
                            className={`w-full p-3 rounded-lg border text-left transition-all duration-200 ${
                              compressionLevel === key
                                ? 'border-green-500 bg-green-50 text-green-700'
                                : 'border-gray-200 hover:border-green-300 text-gray-700'
                            }`}
                            onClick={() => setCompressionLevel(key)}
                          >
                            <div className="font-medium capitalize">{key}</div>
                            <div className="text-sm text-gray-500">{preset.description}</div>
                            <div className="text-xs text-gray-400">Quality: {preset.quality}%</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Output Format */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Output Format</label>
                      <div className="space-y-2">
                        {formatOptions.map((format) => (
                          <button
                            key={format.value}
                            className={`w-full p-3 rounded-lg border text-left transition-all duration-200 ${
                              outputFormat === format.value
                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                : 'border-gray-200 hover:border-blue-300 text-gray-700'
                            }`}
                            onClick={() => setOutputFormat(format.value)}
                          >
                            <div className="font-medium">{format.label}</div>
                            <div className="text-sm text-gray-500">{format.description}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Resize Options */}
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <label className="text-sm font-medium text-gray-700">Resize Image</label>
                        <button
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            enableResize ? 'bg-green-600' : 'bg-gray-200'
                          }`}
                          onClick={() => setEnableResize(!enableResize)}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              enableResize ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>

                      {enableResize && (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm text-gray-600 mb-2">Max Width (px)</label>
                            <input
                              type="number"
                              value={maxWidth}
                              onChange={(e) => setMaxWidth(parseInt(e.target.value) || 1920)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="100"
                              max="4000"
                            />
                          </div>
                          <div>
                            <label className="block text-sm text-gray-600 mb-2">Max Height (px)</label>
                            <input
                              type="number"
                              value={maxHeight}
                              onChange={(e) => setMaxHeight(parseInt(e.target.value) || 1080)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="100"
                              max="4000"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={optimizeImage}
                        disabled={isProcessing}
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? 'Optimizing...' : 'Optimize Image'}
                      </button>
                      <button
                        onClick={resetTool}
                        className="px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md"
                      >
                        Reset
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Preview Section */}
            <div className="lg:col-span-2 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6">
                <h2 className="text-xl font-bold text-white">Preview & Results</h2>
                <p className="text-orange-100 mt-1">Original and optimized image comparison</p>
              </div>
              <div className="p-6">
                {imagePreview ? (
                  <div className="space-y-6">
                    {/* Original Image */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-3">Original Image</h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <img
                          src={imagePreview}
                          alt="Original"
                          className="max-w-full h-auto max-h-64 mx-auto rounded-lg shadow-sm"
                        />
                        <div className="mt-3 text-center text-sm text-gray-600">
                          {imageDimensions.width} × {imageDimensions.height} px • {formatFileSize(originalSize)}
                        </div>
                      </div>
                    </div>

                    {/* Optimized Image */}
                    {optimizedImage && (
                      <div>
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="text-lg font-semibold text-gray-800">Optimized Image</h3>
                          <button
                            onClick={downloadImage}
                            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-md text-sm"
                          >
                            📥 Download
                          </button>
                        </div>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <img
                            src={optimizedImage}
                            alt="Optimized"
                            className="max-w-full h-auto max-h-64 mx-auto rounded-lg shadow-sm"
                          />
                          <div className="mt-3 text-center text-sm text-gray-600">
                            {formatFileSize(optimizedSize)} • {getCompressionRatio()}% smaller
                          </div>
                        </div>

                        {/* Optimization Results */}
                        <div className="bg-green-50 p-4 rounded-lg mt-4">
                          <h4 className="font-medium text-gray-800 mb-3">Optimization Results</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600"><strong>Original Size:</strong> {formatFileSize(originalSize)}</p>
                              <p className="text-gray-600"><strong>Optimized Size:</strong> {formatFileSize(optimizedSize)}</p>
                              <p className="text-green-600 font-medium"><strong>Size Reduction:</strong> {getCompressionRatio()}%</p>
                            </div>
                            <div>
                              <p className="text-gray-600"><strong>Compression:</strong> {compressionLevel}</p>
                              <p className="text-gray-600"><strong>Format:</strong> {getOptimalFormat(selectedImage?.type)}</p>
                              <p className="text-gray-600"><strong>Quality:</strong> {compressionPresets[compressionLevel].quality}%</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">⚡</div>
                    <p className="text-gray-500 text-lg">Upload an image to start optimization</p>
                    <p className="text-gray-400 text-sm mt-2">Reduce file sizes while maintaining quality</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Hidden canvas for image processing */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">About Image Optimization</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Why Optimize Images?</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Image optimization is crucial for web performance. Optimized images load faster,
                  use less bandwidth, and improve user experience while maintaining visual quality.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <strong>Faster Loading:</strong> Reduced file sizes mean quicker page loads
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    <strong>Better SEO:</strong> Search engines favor fast-loading websites
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    <strong>Lower Bandwidth:</strong> Save on hosting and data costs
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Smart Optimization Features</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    Intelligent format selection (JPEG, PNG, WebP)
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    Quality-based compression with presets
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    Automatic resizing for web standards
                  </li>
                  <li className="flex items-center">
                    <span className="text-teal-500 mr-2">•</span>
                    Transparency preservation when needed
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageOptimizer;
