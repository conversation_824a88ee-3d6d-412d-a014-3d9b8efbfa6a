import React, { useState } from 'react';
import SEO from '../SEO';
import QRTypeSelector from './QRTypeSelector';
import QRFormFields from './QRFormFields';
import QRPreview from './QRPreview';
import QRCustomization from './QRCustomization';
import QRHistory from './QRHistory';
import { useFormData } from './hooks/useFormData';
import { useQRGenerator } from './hooks/useQRGenerator';
import { useQRHistory } from './hooks/useQRHistory';

const QRCodeGenerator = () => {
  const [qrType, setQrType] = useState('text');
  const [showHistory, setShowHistory] = useState(false);

  // Custom hooks
  const { formData, updateFormData } = useFormData();
  const { history, addToHistory, clearHistory } = useQRHistory();
  
  // QR generation hook with callback
  const {
    canvasRef,
    qrOptions,
    setQrOptions,
    generatedQR,
    qrData,
    isGenerating,
    generateQR
  } = useQRGenerator(qrType, formData, (qrDataURL, data, saveToHistory) => {
    if (saveToHistory) {
      addToHistory(qrType, data, qrDataURL);
    }
  });

  return (
    <>
      <SEO
        title="QR Code Generator - Create Custom QR Codes for Any Purpose | ToollyHub"
        description="Generate QR codes for text, URLs, emails, phone numbers, WiFi, contacts, and events. Customize colors, size, and download high-quality QR codes instantly. Perfect for marketing, business cards, menus, WiFi sharing, and contact information. Free QR code generator with advanced customization options."
        keywords="QR code generator, QR code creator, generate QR code, QR code maker, text to QR, URL QR code, email QR code, WiFi QR code, contact QR code, event QR code, custom QR code, QR code customization, QR code colors, QR code download, marketing QR code, business QR code, menu QR code, free QR generator, online QR code, QR code tool"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "QR Code Generator",
          "description": "Generate custom QR codes for text, URLs, contacts, and more with advanced customization",
          "url": "https://toollyhub.com/qr-code-generator",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Text QR codes",
            "URL QR codes",
            "Email QR codes",
            "Phone number QR codes",
            "WiFi QR codes",
            "Contact QR codes",
            "Event QR codes",
            "Color customization",
            "Size adjustment",
            "High-quality download",
            "History tracking"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'QR Code Generator' },
          { property: 'og:image:alt', content: 'QR Code Generator - Create Custom QR Codes for Any Purpose' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-white">QR Code Generator</h2>
                <p className="text-indigo-100 mt-1">Create QR codes for any content type</p>
              </div>
              <button
                onClick={() => setShowHistory(!showHistory)}
                className="relative p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-200 group"
                title="View History"
              >
                <span className="text-2xl text-white">📚</span>
                {history.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                    {history.length > 9 ? '9+' : history.length}
                  </span>
                )}
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* QR Type Selection */}
            <QRTypeSelector qrType={qrType} setQrType={setQrType} />

            {/* Main Content Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Side - Form and Controls */}
              <QRFormFields
                qrType={qrType}
                formData={formData}
                updateFormData={updateFormData}
                generateQR={generateQR}
                generatedQR={generatedQR}
              />

              {/* Right Side - QR Preview and Customization */}
              <div className="space-y-6">
                <QRPreview
                  canvasRef={canvasRef}
                  generatedQR={generatedQR}
                  qrData={qrData}
                  qrType={qrType}
                  isGenerating={isGenerating}
                />
                
                <QRCustomization
                  qrOptions={qrOptions}
                  setQrOptions={setQrOptions}
                  qrData={qrData}
                />
              </div>
            </div>

            {/* History Modal/Overlay */}
            {showHistory && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                onClick={() => setShowHistory(false)}
              >
                <div
                  className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-white">QR Code History</h3>
                      <button
                        onClick={() => setShowHistory(false)}
                        className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-200"
                      >
                        <span className="text-white text-xl">✕</span>
                      </button>
                    </div>
                  </div>
                  <div className="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
                    <QRHistory
                      history={history}
                      clearHistory={clearHistory}
                    />
                    {history.length === 0 && (
                      <div className="text-center py-12 text-gray-500">
                        <div className="text-6xl mb-4">📚</div>
                        <p className="text-lg">No QR codes saved yet</p>
                        <p className="text-sm mt-2">Generate and save QR codes to see them here</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default QRCodeGenerator;
