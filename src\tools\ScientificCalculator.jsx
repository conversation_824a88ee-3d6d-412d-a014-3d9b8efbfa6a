import React, { useState } from 'react';
import SEO from '../components/SEO';
import { evaluate } from 'mathjs';

const ScientificCalculator = () => {
  const [display, setDisplay] = useState('0');
  const [equation, setEquation] = useState('');
  const [hasDecimal, setHasDecimal] = useState(false);
  const [lastWasOperator, setLastWasOperator] = useState(false);
  const [isRadians, setIsRadians] = useState(true);

  const handleNumber = (number) => {
    if (display === '0' || lastWasOperator) {
      setDisplay(number);
      setLastWasOperator(false);
    } else {
      setDisplay(display + number);
    }
  };

  const handleOperator = (operator) => {
    setEquation(display + ' ' + operator + ' ');
    setLastWasOperator(true);
    setHasDecimal(false);
  };

  const handleFunction = (func) => {
    let input = parseFloat(display);
    let result;
    
    try {
      switch (func) {
        case 'sin':
          result = isRadians ? Math.sin(input) : Math.sin(input * Math.PI / 180);
          break;
        case 'cos':
          result = isRadians ? Math.cos(input) : Math.cos(input * Math.PI / 180);
          break;
        case 'tan':
          result = isRadians ? Math.tan(input) : Math.tan(input * Math.PI / 180);
          break;
        case 'log':
          result = Math.log10(input);
          break;
        case 'ln':
          result = Math.log(input);
          break;
        case 'sqrt':
          result = Math.sqrt(input);
          break;
        case 'square':
          result = Math.pow(input, 2);
          break;
        case 'cube':
          result = Math.pow(input, 3);
          break;
        case 'pi':
          result = Math.PI;
          break;
        case 'e':
          result = Math.E;
          break;
        default:
          return;
      }
      
      setDisplay(String(result.toFixed(8).replace(/\.?0+$/, '')));
      setHasDecimal(String(result).includes('.'));
      setLastWasOperator(false);
    } catch (error) {
      setDisplay('Error');
    }
  };

  const handleDecimal = () => {
    if (!hasDecimal) {
      setDisplay(display + '.');
      setHasDecimal(true);
    }
  };

  const handleEqual = () => {
    try {
      const result = evaluate(equation + display);
      setDisplay(String(result.toFixed(8).replace(/\.?0+$/, '')));
      setEquation('');
      setHasDecimal(String(result).includes('.'));
      setLastWasOperator(false);
    } catch (error) {
      setDisplay('Error');
      setEquation('');
    }
  };

  const handleClear = () => {
    setDisplay('0');
    setEquation('');
    setHasDecimal(false);
    setLastWasOperator(false);
  };

  const handleDelete = () => {
    if (display.length > 1) {
      const newDisplay = display.slice(0, -1);
      setDisplay(newDisplay);
      setHasDecimal(newDisplay.includes('.'));
    } else {
      setDisplay('0');
      setHasDecimal(false);
    }
  };

  const toggleAngleMode = () => {
    setIsRadians(!isRadians);
  };

  return (
    <>
      <SEO
        title="Scientific Calculator - Free Advanced Math Calculator Online | ToollyHub"
        description="Free online scientific calculator with advanced mathematical functions. Perform complex calculations, trigonometry, logarithms, exponentials, square roots, and more. Perfect for students, engineers, scientists, and professionals. No download required - works in any web browser."
        keywords="scientific calculator, advanced calculator, trigonometry calculator, logarithm calculator, math calculator, engineering calculator, online calculator, complex calculator, sin cos tan calculator, log calculator, exponential calculator, square root calculator, power calculator, factorial calculator, pi calculator, scientific notation calculator, advanced math tool, engineering tool, student calculator, professional calculator, free scientific calculator"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Scientific Calculator",
          "description": "Free online scientific calculator with advanced mathematical functions",
          "url": "https://toollyhub.com/scientific-calculator",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Basic arithmetic operations",
            "Trigonometric functions (sin, cos, tan)",
            "Logarithmic functions",
            "Exponential functions",
            "Square root and power functions",
            "Factorial calculations",
            "Scientific notation",
            "Memory functions"
          ],
          "audience": {
            "@type": "Audience",
            "audienceType": ["Students", "Engineers", "Scientists", "Professionals"]
          }
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Scientific Calculator' },
          { property: 'og:image:alt', content: 'Scientific Calculator - Free Advanced Math Calculator Online' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
            <h2 className="text-2xl font-bold text-white">Scientific Calculator</h2>
            <p className="text-blue-100 mt-1">Advanced mathematical calculations</p>
          </div>
          
          <div className="p-6 space-y-6">
            <div className="bg-gray-50 p-6 rounded-xl shadow-inner border border-gray-200">
              <div className="flex justify-between items-center mb-2">
                <button
                  onClick={() => setIsRadians(!isRadians)}
                  className={`text-sm px-4 py-2 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 ${isRadians ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
                >
                  {isRadians ? 'RAD' : 'DEG'}
                </button>
                <div className="text-gray-600 text-sm font-mono">{equation}</div>
              </div>
              <div className="text-right text-3xl font-bold text-gray-800 break-all font-mono mt-2">{display}</div>
            </div>

            <div className="grid grid-cols-5 gap-3 mb-4">
              {['sin', 'cos', 'tan', 'log', 'ln'].map((func) => (
                <button
                  key={func}
                  onClick={() => handleFunction(func)}
                  className="bg-blue-100 text-blue-700 p-3 rounded-xl hover:bg-blue-200 transition-all duration-200 transform hover:scale-105 font-semibold shadow-md"
                >
                  {func}
                </button>
              ))}
            </div>

            <div className="grid grid-cols-5 gap-3 mb-4">
              {['sqrt', 'square', 'cube', 'pi', 'e'].map((func) => (
                <button
                  key={func}
                  onClick={() => handleFunction(func)}
                  className="bg-blue-100 text-blue-700 p-3 rounded-xl hover:bg-blue-200 transition-all duration-200 transform hover:scale-105 font-semibold shadow-md"
                >
                  {func === 'sqrt' ? '√' :
                   func === 'square' ? 'x²' :
                   func === 'cube' ? 'x³' :
                   func === 'pi' ? 'π' : 'e'}
                </button>
              ))}
            </div>

            <div className="grid grid-cols-5 gap-3">
              {/* Standard Calculator Buttons */}
              <button onClick={handleClear} className="col-span-2 bg-red-500 text-white p-4 rounded-xl hover:bg-red-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                Clear
              </button>
              <button onClick={handleDelete} className="bg-gray-200 text-gray-700 p-4 rounded-xl hover:bg-gray-300 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md">
                ⌫
              </button>
              <button onClick={() => handleOperator('/')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                ÷
              </button>
              <button onClick={() => handleOperator('*')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                ×
              </button>

              {[7, 8, 9].map((num) => (
                <button
                  key={num}
                  onClick={() => handleNumber(num.toString())}
                  className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200"
                >
                  {num}
                </button>
              ))}
              <button onClick={() => handleOperator('-')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                -
              </button>

              {[4, 5, 6].map((num) => (
                <button
                  key={num}
                  onClick={() => handleNumber(num.toString())}
                  className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200"
                >
                  {num}
                </button>
              ))}
              <button onClick={() => handleOperator('+')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                +
              </button>

              {[1, 2, 3].map((num) => (
                <button
                  key={num}
                  onClick={() => handleNumber(num.toString())}
                  className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200"
                >
                  {num}
                </button>
              ))}
              <button onClick={handleEqual} className="row-span-2 bg-green-500 text-white p-4 rounded-xl hover:bg-green-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                =
              </button>

              <button onClick={() => handleNumber('0')} className="col-span-2 bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200">
                0
              </button>
              <button onClick={handleDecimal} className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200">
                .
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ScientificCalculator;