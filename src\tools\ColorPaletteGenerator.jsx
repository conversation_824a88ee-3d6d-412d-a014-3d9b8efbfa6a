import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';
import html2canvas from 'html2canvas';

const ColorPaletteGenerator = () => {
  const [colors, setColors] = useState([]);
  const [lockedColors, setLockedColors] = useState({});
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  const [favorites, setFavorites] = useState([]);
  const [showCopied, setShowCopied] = useState(null);

  // Load saved state from localStorage
  useEffect(() => {
    const savedColors = localStorage.getItem('colorPalette');
    const savedLocks = localStorage.getItem('lockedColors');
    const savedTheme = localStorage.getItem('isDarkTheme');
    const savedFavorites = localStorage.getItem('favoritePalettes');

    if (savedColors) setColors(JSON.parse(savedColors));
    if (savedLocks) setLockedColors(JSON.parse(savedLocks));
    if (savedTheme) setIsDarkTheme(JSON.parse(savedTheme));
    if (savedFavorites) setFavorites(JSON.parse(savedFavorites));
  }, []);

  // Save state to localStorage
  useEffect(() => {
    localStorage.setItem('colorPalette', JSON.stringify(colors));
    localStorage.setItem('lockedColors', JSON.stringify(lockedColors));
    localStorage.setItem('isDarkTheme', JSON.stringify(isDarkTheme));
    localStorage.setItem('favoritePalettes', JSON.stringify(favorites));
  }, [colors, lockedColors, isDarkTheme, favorites]);

  // Generate random color
  const generateRandomColor = () => {
    return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
  };

  // Generate new palette
  const generatePalette = () => {
    const newColors = Array(5).fill().map((_, index) => 
      lockedColors[index] ? colors[index] : generateRandomColor()
    );
    setColors(newColors);
  };

  // Initialize or regenerate palette
  useEffect(() => {
    if (colors.length === 0) {
      generatePalette();
    }
  }, []);

  // Toggle color lock
  const toggleLock = (index) => {
    setLockedColors(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Copy color to clipboard
  const copyToClipboard = async (color, index) => {
    try {
      await navigator.clipboard.writeText(color);
      setShowCopied(index);
      setTimeout(() => setShowCopied(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  // Save current palette to favorites
  const saveToFavorites = () => {
    const newFavorite = {
      id: Date.now(),
      colors: [...colors],
      date: new Date().toISOString()
    };
    setFavorites(prev => [...prev, newFavorite]);
  };

  // Remove from favorites
  const removeFromFavorites = (id) => {
    setFavorites(prev => prev.filter(fav => fav.id !== id));
  };

  // Share palette as image
  const shareAsImage = async () => {
    const element = document.getElementById('palette-container');
    try {
      const canvas = await html2canvas(element);
      const image = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = 'color-palette.png';
      link.href = image;
      link.click();
    } catch (err) {
      console.error('Failed to generate image: ', err);
    }
  };

  // Share palette as link
  const shareAsLink = () => {
    const paletteString = colors.join(',');
    const url = `${window.location.origin}${window.location.pathname}?palette=${paletteString}`;
    navigator.clipboard.writeText(url);
    alert('Palette link copied to clipboard!');
  };

  // Load palette from URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const paletteParam = params.get('palette');
    if (paletteParam) {
      const paletteColors = paletteParam.split(',');
      if (paletteColors.length === 5) {
        setColors(paletteColors);
      }
    }
  }, []);

  return (
    <>
      <SEO
        title="Color Palette Generator - Create Beautiful Color Schemes | ToollyHub"
        description="Generate beautiful color palettes and color schemes for your design projects. Lock favorite colors, copy hex codes, save palettes, and share with others. Perfect for web design, graphic design, branding, and creative projects. Free color scheme generator with advanced features."
        keywords="color palette generator, color scheme generator, color picker, hex color codes, color combinations, color palette creator, color sharing, color tools, design tools, web design colors, graphic design palette, branding colors, color harmony, color theory, design inspiration, color swatches, color schemes, random colors, color generator, free color tool, design palette, creative colors"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Color Palette Generator",
          "description": "Generate beautiful color palettes and color schemes for design projects",
          "url": "https://toollyhub.com/color-palette-generator",
          "applicationCategory": "DesignApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Random color palette generation",
            "Color locking functionality",
            "Hex color code display",
            "Copy to clipboard",
            "Save favorite palettes",
            "Share color schemes",
            "Dark/light theme toggle",
            "Export options"
          ],
          "audience": {
            "@type": "Audience",
            "audienceType": ["Designers", "Web Developers", "Artists", "Creatives"]
          }
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Color Palette Generator' },
          { property: 'og:image:alt', content: 'Color Palette Generator - Create Beautiful Color Schemes' }
        ]}
      />
      <div className={`container mx-auto p-6 ${isDarkTheme ? 'bg-gray-900 text-white' : 'bg-gray-100'}`}>
        <div className="max-w-4xl mx-auto">
          {/* Theme Toggle */}
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setIsDarkTheme(!isDarkTheme)}
              className={`p-2 rounded-full ${isDarkTheme ? 'bg-gray-700' : 'bg-gray-200'}`}
            >
              {isDarkTheme ? '🌞' : '🌙'}
            </button>
          </div>

          {/* Color Palette */}
          <div id="palette-container" className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
            {colors.map((color, index) => (
              <div
                key={index}
                className="relative group"
                onClick={() => copyToClipboard(color, index)}
              >
                <div
                  className="h-48 rounded-lg shadow-lg cursor-pointer transform transition-transform hover:scale-105"
                  style={{ backgroundColor: color }}
                >
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleLock(index);
                    }}
                    className={`absolute top-2 right-2 p-2 rounded-full ${
                      isDarkTheme ? 'bg-gray-800' : 'bg-white'
                    } shadow-md`}
                  >
                    {lockedColors[index] ? '🔒' : '🔓'}
                  </button>
                </div>
                <div className={`mt-2 text-center ${isDarkTheme ? 'text-white' : 'text-gray-800'}`}>
                  {showCopied === index ? (
                    <span className="text-green-500">Copied!</span>
                  ) : (
                    <span className="font-mono">{color}</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <button
              onClick={generatePalette}
              className={`px-6 py-2 rounded-lg shadow-md ${
                isDarkTheme ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white transition-colors`}
            >
              Generate New Palette
            </button>
            <button
              onClick={saveToFavorites}
              className={`px-6 py-2 rounded-lg shadow-md ${
                isDarkTheme ? 'bg-green-600 hover:bg-green-700' : 'bg-green-500 hover:bg-green-600'
              } text-white transition-colors`}
            >
              Save to Favorites
            </button>
            <button
              onClick={shareAsImage}
              className={`px-6 py-2 rounded-lg shadow-md ${
                isDarkTheme ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-500 hover:bg-purple-600'
              } text-white transition-colors`}
            >
              Share as Image
            </button>
            <button
              onClick={shareAsLink}
              className={`px-6 py-2 rounded-lg shadow-md ${
                isDarkTheme ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-indigo-500 hover:bg-indigo-600'
              } text-white transition-colors`}
            >
              Share as Link
            </button>
          </div>

          {/* Favorites Section */}
          {favorites.length > 0 && (
            <div className="mt-8">
              <h2 className={`text-2xl font-bold mb-4 ${isDarkTheme ? 'text-white' : 'text-gray-800'}`}>
                Favorite Palettes
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {favorites.map((favorite) => (
                  <div
                    key={favorite.id}
                    className={`p-4 rounded-lg shadow-md ${
                      isDarkTheme ? 'bg-gray-800' : 'bg-white'
                    }`}
                  >
                    <div className="flex gap-2 mb-2">
                      {favorite.colors.map((color, index) => (
                        <div
                          key={index}
                          className="h-8 flex-1 rounded cursor-pointer"
                          style={{ backgroundColor: color }}
                          onClick={() => copyToClipboard(color)}
                        />
                      ))}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className={`text-sm ${isDarkTheme ? 'text-gray-300' : 'text-gray-600'}`}>
                        {new Date(favorite.date).toLocaleDateString()}
                      </span>
                      <button
                        onClick={() => removeFromFavorites(favorite.id)}
                        className="text-red-500 hover:text-red-600"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ColorPaletteGenerator; 