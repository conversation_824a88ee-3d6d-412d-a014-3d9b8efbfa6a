import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const UnitConverter = () => {
  const [inputValue, setInputValue] = useState('');
  const [fromUnit, setFromUnit] = useState('meters');
  const [toUnit, setToUnit] = useState('feet');
  const [result, setResult] = useState('');
  const [category, setCategory] = useState('length'); // 'length', 'weight', 'temperature'
  const [error, setError] = useState('');

  // Unit conversion factors and options
  const unitCategories = {
    length: {
      name: 'Length',
      units: ['millimeters', 'centimeters', 'meters', 'kilometers', 'inches', 'feet', 'yards', 'miles']
    },
    weight: {
      name: 'Weight',
      units: ['milligrams', 'grams', 'kilograms', 'tonnes', 'ounces', 'pounds', 'stone', 'tons']
    },
    temperature: {
      name: 'Temperature',
      units: ['celsius', 'fahrenheit', 'kelvin']
    },
    volume: {
      name: 'Volume',
      units: ['milliliters', 'liters', 'cubic-meters', 'fluid-ounces', 'cups', 'pints', 'gallons']
    }
  };

  // Reset units when category changes
  useEffect(() => {
    const defaultUnits = unitCategories[category].units;
    setFromUnit(defaultUnits[0]);
    setToUnit(defaultUnits[1]);
    setResult('');
  }, [category]);

  const handleCategoryChange = (e) => {
    setCategory(e.target.value);
    setInputValue('');
    setResult('');
    setError('');
  };

  const convert = () => {
    if (!inputValue.trim()) {
      setError('Please enter a value to convert');
      setResult('');
      return;
    }

    if (isNaN(inputValue)) {
      setError('Please enter a valid number');
      setResult('');
      return;
    }

    setError('');
    const value = parseFloat(inputValue);
    let convertedValue;

    // Convert to base unit first (e.g., meters for length)
    let baseValue;

    // Temperature conversion needs special handling
    if (category === 'temperature') {
      convertedValue = convertTemperature(value, fromUnit, toUnit);
    } else {
      // For non-temperature units, use conversion factors
      baseValue = convertToBaseUnit(value, fromUnit, category);
      convertedValue = convertFromBaseUnit(baseValue, toUnit, category);
    }

    setResult(convertedValue.toFixed(4));
  };

  // Convert from any unit to the base unit
  const convertToBaseUnit = (value, unit, category) => {
    switch (category) {
      case 'length':
        switch (unit) {
          case 'millimeters': return value * 0.001;
          case 'centimeters': return value * 0.01;
          case 'meters': return value;
          case 'kilometers': return value * 1000;
          case 'inches': return value * 0.0254;
          case 'feet': return value * 0.3048;
          case 'yards': return value * 0.9144;
          case 'miles': return value * 1609.34;
          default: return value;
        }
      case 'weight':
        switch (unit) {
          case 'milligrams': return value * 0.000001;
          case 'grams': return value * 0.001;
          case 'kilograms': return value;
          case 'tonnes': return value * 1000;
          case 'ounces': return value * 0.0283495;
          case 'pounds': return value * 0.453592;
          case 'stone': return value * 6.35029;
          case 'tons': return value * 907.185;
          default: return value;
        }
      case 'volume':
        switch (unit) {
          case 'milliliters': return value * 0.001;
          case 'liters': return value;
          case 'cubic-meters': return value * 1000;
          case 'fluid-ounces': return value * 0.0295735;
          case 'cups': return value * 0.236588;
          case 'pints': return value * 0.473176;
          case 'gallons': return value * 3.78541;
          default: return value;
        }
      default: return value;
    }
  };

  // Convert from base unit to any unit
  const convertFromBaseUnit = (value, unit, category) => {
    switch (category) {
      case 'length':
        switch (unit) {
          case 'millimeters': return value * 1000;
          case 'centimeters': return value * 100;
          case 'meters': return value;
          case 'kilometers': return value * 0.001;
          case 'inches': return value * 39.3701;
          case 'feet': return value * 3.28084;
          case 'yards': return value * 1.09361;
          case 'miles': return value * 0.000621371;
          default: return value;
        }
      case 'weight':
        switch (unit) {
          case 'milligrams': return value * 1000000;
          case 'grams': return value * 1000;
          case 'kilograms': return value;
          case 'tonnes': return value * 0.001;
          case 'ounces': return value * 35.274;
          case 'pounds': return value * 2.20462;
          case 'stone': return value * 0.157473;
          case 'tons': return value * 0.00110231;
          default: return value;
        }
      case 'volume':
        switch (unit) {
          case 'milliliters': return value * 1000;
          case 'liters': return value;
          case 'cubic-meters': return value * 0.001;
          case 'fluid-ounces': return value * 33.814;
          case 'cups': return value * 4.22675;
          case 'pints': return value * 2.11338;
          case 'gallons': return value * 0.264172;
          default: return value;
        }
      default: return value;
    }
  };

  // Handle temperature conversion specifically
  const convertTemperature = (value, fromUnit, toUnit) => {
    // Convert to Celsius first
    let celsiusValue;
    switch (fromUnit) {
      case 'celsius':
        celsiusValue = value;
        break;
      case 'fahrenheit':
        celsiusValue = (value - 32) * (5/9);
        break;
      case 'kelvin':
        celsiusValue = value - 273.15;
        break;
      default:
        celsiusValue = value;
    }

    // Convert from Celsius to target unit
    switch (toUnit) {
      case 'celsius':
        return celsiusValue;
      case 'fahrenheit':
        return (celsiusValue * (9/5)) + 32;
      case 'kelvin':
        return celsiusValue + 273.15;
      default:
        return celsiusValue;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Unit Converter - Free Online Measurement Converter Tool | ToollyHub"
        description="Free unit converter for all your measurement needs. Convert between different units of length, weight, temperature, and volume. Supports metric and imperial systems with instant, accurate conversions. Perfect for cooking, construction, science, and everyday use."
        keywords="unit converter, measurement converter, metric converter, imperial converter, length converter, weight converter, temperature converter, volume converter, unit conversion tool, convert units online, measurement tool, metric to imperial, imperial to metric, distance converter, mass converter, celsius fahrenheit, cooking converter, construction measurements, scientific conversions, free unit converter, online converter tool"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Unit Converter",
          "description": "Free unit converter for length, weight, temperature, and volume measurements",
          "url": "https://toollyhub.com/unit-converter",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Length conversion (meters, feet, inches, miles, etc.)",
            "Weight conversion (kilograms, pounds, ounces, etc.)",
            "Temperature conversion (Celsius, Fahrenheit, Kelvin)",
            "Volume conversion (liters, gallons, cups, etc.)",
            "Metric and Imperial systems",
            "Instant conversion results",
            "High precision calculations"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Unit Converter' },
          { property: 'og:image:alt', content: 'Unit Converter - Free Online Measurement Converter Tool' }
        ]}
      />

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Unit Converter</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Convert between different units of measurement with precision. Support for length, weight, temperature, and volume conversions.
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
            <h2 className="text-2xl font-bold text-white">Convert Units</h2>
            <p className="text-blue-100 mt-1">Select category and enter values to convert</p>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Category</label>
                <select
                  value={category}
                  onChange={handleCategoryChange}
                  className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  {Object.keys(unitCategories).map(cat => (
                    <option key={cat} value={cat}>{unitCategories[cat].name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="inputValue" className="block text-sm font-medium text-gray-700 mb-2">Value to Convert</label>
                <input
                  type="text"
                  id="inputValue"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter value to convert"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="fromUnit" className="block text-sm font-medium text-gray-700 mb-2">From Unit</label>
                <select
                  id="fromUnit"
                  value={fromUnit}
                  onChange={(e) => setFromUnit(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  {unitCategories[category].units.map(unit => (
                    <option key={unit} value={unit}>{unit.charAt(0).toUpperCase() + unit.slice(1)}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="toUnit" className="block text-sm font-medium text-gray-700 mb-2">To Unit</label>
                <select
                  id="toUnit"
                  value={toUnit}
                  onChange={(e) => setToUnit(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  {unitCategories[category].units.map(unit => (
                    <option key={unit} value={unit}>{unit.charAt(0).toUpperCase() + unit.slice(1)}</option>
                  ))}
                </select>
              </div>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl">
                {error}
              </div>
            )}

            <button
              onClick={convert}
              className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
            >
              Convert Units
            </button>

            {result && (
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Conversion Result</h3>
                <div className="p-6 bg-gray-50 rounded-xl border border-gray-100">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-700">
                      {inputValue} {fromUnit} = {result} {toUnit}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Use Our Unit Converter?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Supported Categories</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Length (meters, feet, inches, miles, etc.)
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  Weight (kilograms, pounds, ounces, etc.)
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Temperature (Celsius, Fahrenheit, Kelvin)
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Volume (liters, gallons, cups, etc.)
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Key Features</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Accurate conversion algorithms</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Support for metric and imperial units</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Real-time conversion results</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Easy-to-use interface</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnitConverter;
