import { useEffect } from 'react';

const SEO = ({
  title,
  description,
  keywords,
  ogImage = '/logo.svg',
  ogUrl = window.location.href,
  type = 'website',
  canonical = window.location.href,
  author = 'ToollyHub',
  robots = 'index, follow',
  structuredData = null,
  additionalMeta = []
}) => {
  useEffect(() => {
    // Update title
    document.title = title;
    
    // Update meta tags
    const updateMetaTag = (name, content) => {
      let meta = document.querySelector(`meta[name="${name}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('name', name);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    const updatePropertyTag = (property, content) => {
      let meta = document.querySelector(`meta[property="${property}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('property', property);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Update primary meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', author);
    updateMetaTag('robots', robots);
    updateMetaTag('viewport', 'width=device-width, initial-scale=1.0');

    // Update canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', canonical);

    // Update Open Graph tags
    updatePropertyTag('og:title', title);
    updatePropertyTag('og:description', description);
    updatePropertyTag('og:type', type);
    updatePropertyTag('og:url', ogUrl);
    updatePropertyTag('og:image', ogImage);
    updatePropertyTag('og:site_name', 'ToollyHub');
    updatePropertyTag('og:locale', 'en_US');

    // Update Twitter tags
    updatePropertyTag('twitter:card', 'summary_large_image');
    updatePropertyTag('twitter:title', title);
    updatePropertyTag('twitter:description', description);
    updatePropertyTag('twitter:image', ogImage);
    updatePropertyTag('twitter:site', '@ToollyHub');
    updatePropertyTag('twitter:creator', '@ToollyHub');

    // Add additional meta tags
    additionalMeta.forEach(meta => {
      if (meta.name) {
        updateMetaTag(meta.name, meta.content);
      } else if (meta.property) {
        updatePropertyTag(meta.property, meta.content);
      }
    });

    // Add structured data if provided
    if (structuredData) {
      let structuredDataScript = document.querySelector('script[type="application/ld+json"]');
      if (!structuredDataScript) {
        structuredDataScript = document.createElement('script');
        structuredDataScript.setAttribute('type', 'application/ld+json');
        document.head.appendChild(structuredDataScript);
      }
      structuredDataScript.textContent = JSON.stringify(structuredData);
    }

    // Cleanup function
    return () => {
      document.title = 'ToollyHub - Your Ultimate Tool Management Solution';
    };
  }, [title, description, keywords, ogImage, ogUrl, type, canonical, author, robots, structuredData, additionalMeta]);

  return null;
};

export default SEO; 