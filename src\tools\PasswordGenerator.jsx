import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const PasswordGenerator = () => {
  const [password, setPassword] = useState('');
  const [strength, setStrength] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [length, setLength] = useState(12);
  const [options, setOptions] = useState({
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true
  });

  // Password strength checker
  useEffect(() => {
    if (!password) {
      setStrength(null);
      setSuggestions([]);
      return;
    }

    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      numbers: /[0-9]/.test(password),
      symbols: /[^A-Za-z0-9]/.test(password)
    };

    // Calculate strength
    const score = Object.values(checks).filter(Boolean).length;
    let strengthLevel;
    let strengthColor;
    let newSuggestions = [];

    if (score <= 2) {
      strengthLevel = 'Weak';
      strengthColor = 'red';
    } else if (score <= 4) {
      strengthLevel = 'Medium';
      strengthColor = 'yellow';
    } else {
      strengthLevel = 'Strong';
      strengthColor = 'green';
    }

    // Generate suggestions
    if (!checks.length) newSuggestions.push('Make your password at least 8 characters long');
    if (!checks.uppercase) newSuggestions.push('Add uppercase letters (A-Z)');
    if (!checks.lowercase) newSuggestions.push('Add lowercase letters (a-z)');
    if (!checks.numbers) newSuggestions.push('Add numbers (0-9)');
    if (!checks.symbols) newSuggestions.push('Add special characters (!@#$%^&*)');

    setStrength({ level: strengthLevel, color: strengthColor });
    setSuggestions(newSuggestions);
  }, [password]);

  // Generate random password
  const generatePassword = () => {
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const numberChars = '0123456789';
    const symbolChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    let chars = '';
    if (options.uppercase) chars += uppercaseChars;
    if (options.lowercase) chars += lowercaseChars;
    if (options.numbers) chars += numberChars;
    if (options.symbols) chars += symbolChars;

    if (chars === '') {
      setSuggestions(['Please select at least one character type']);
      return;
    }

    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // Ensure the generated password meets all selected criteria
    if (options.uppercase && !/[A-Z]/.test(result)) return generatePassword();
    if (options.lowercase && !/[a-z]/.test(result)) return generatePassword();
    if (options.numbers && !/[0-9]/.test(result)) return generatePassword();
    if (options.symbols && !/[^A-Za-z0-9]/.test(result)) return generatePassword();

    setGeneratedPassword(result);
    setPassword(result);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Password copied to clipboard!');
    }).catch(err => {
      console.error('Failed to copy: ', err);
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Password Generator & Strength Checker - Create Secure Passwords | ToollyHub"
        description="Generate strong, secure passwords and check password strength in real-time. Create random passwords with customizable length and character sets. Get instant feedback and suggestions to improve your password security. Perfect for account security, business use, and personal protection."
        keywords="password generator, password strength checker, secure password, random password generator, password security, password strength meter, password suggestions, password complexity, strong password generator, secure password creator, password maker, random password, password tool, cybersecurity, account security, password protection, safe password, complex password, password analyzer, security tool, free password generator"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Password Generator & Strength Checker",
          "description": "Generate strong, secure passwords and check password strength in real-time",
          "url": "https://toollyhub.com/password-generator",
          "applicationCategory": "SecurityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Random password generation",
            "Customizable password length",
            "Character set options (uppercase, lowercase, numbers, symbols)",
            "Real-time password strength checking",
            "Security suggestions",
            "Password complexity analysis",
            "Instant feedback",
            "Copy to clipboard"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Password Generator' },
          { property: 'og:image:alt', content: 'Password Generator & Strength Checker - Create Secure Passwords' }
        ]}
      />

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Password Generator & Strength Checker</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Generate strong, secure passwords and check password strength in real-time. Get instant feedback to improve your password security.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Password Strength Checker */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
              <h2 className="text-2xl font-bold text-white">Password Strength Checker</h2>
              <p className="text-green-100 mt-1">Enter a password to check its security level</p>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Enter Password
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                    placeholder="Enter or generate a password"
                  />
                  {password && (
                    <button
                      onClick={() => copyToClipboard(password)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 p-1"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                        <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>

              {strength && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Password Strength:</span>
                    <span className={`text-sm font-semibold ${
                      strength.color === 'red' ? 'text-red-600' :
                      strength.color === 'yellow' ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {strength.level}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${
                        strength.color === 'red' ? 'bg-red-500' :
                        strength.color === 'yellow' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{
                        width: strength.level === 'Weak' ? '33%' : strength.level === 'Medium' ? '66%' : '100%'
                      }}
                    ></div>
                  </div>
                </div>
              )}

              {suggestions.length > 0 && (
                <div className="p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <p className="text-sm font-semibold text-gray-700 mb-2">Suggestions to improve:</p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-gray-400 mr-2">•</span>
                        <span>{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Password Generator */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
              <h2 className="text-2xl font-bold text-white">Password Generator</h2>
              <p className="text-blue-100 mt-1">Create strong, secure passwords instantly</p>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Password Length: <span className="font-semibold text-blue-600">{length}</span>
                </label>
                <input
                  type="range"
                  min="8"
                  max="32"
                  value={length}
                  onChange={(e) => setLength(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>8</span>
                  <span>32</span>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <h3 className="text-sm font-medium text-gray-700">Include Characters:</h3>
                <div className="grid grid-cols-1 gap-3">
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.uppercase}
                      onChange={(e) => setOptions({...options, uppercase: e.target.checked})}
                      className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="ml-3 text-gray-700">Uppercase Letters (A-Z)</span>
                  </label>
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.lowercase}
                      onChange={(e) => setOptions({...options, lowercase: e.target.checked})}
                      className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="ml-3 text-gray-700">Lowercase Letters (a-z)</span>
                  </label>
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.numbers}
                      onChange={(e) => setOptions({...options, numbers: e.target.checked})}
                      className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="ml-3 text-gray-700">Numbers (0-9)</span>
                  </label>
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.symbols}
                      onChange={(e) => setOptions({...options, symbols: e.target.checked})}
                      className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="ml-3 text-gray-700">Symbols (!@#$%^&*)</span>
                  </label>
                </div>
              </div>

              <button
                onClick={generatePassword}
                className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
              >
                Generate Secure Password
              </button>
            </div>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Use Our Password Generator?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Security Features</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  Cryptographically secure random generation
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Real-time password strength analysis
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Customizable character sets
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  No server storage - generated locally
                </li>
                <li className="flex items-center">
                  <span className="text-red-500 mr-2">•</span>
                  Instant security recommendations
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Best Practices</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Use unique passwords for each account</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Include all character types for maximum security</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Use at least 12 characters for strong passwords</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Store passwords in a secure password manager</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Enable two-factor authentication when available</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordGenerator; 