import React, { useState } from 'react';
import SEO from '../components/SEO';

const PercentageCalculator = () => {
  // State for different calculation types
  const [percentOfValue, setPercentOfValue] = useState({ percent: '', value: '', result: null });
  const [valueIsPercent, setValueIsPercent] = useState({ value: '', percent: '', result: null });
  const [percentChange, setPercentChange] = useState({ oldValue: '', newValue: '', result: null });
  const [discount, setDiscount] = useState({ originalPrice: '', discountPercent: '', result: null });
  
  // Calculate percent of a value (e.g., 20% of 100 = 20)
  const calculatePercentOfValue = (e) => {
    e.preventDefault();
    const { percent, value } = percentOfValue;
    if (!percent || !value) return;
    
    const result = (parseFloat(percent) / 100) * parseFloat(value);
    setPercentOfValue({ ...percentOfValue, result: result.toFixed(2) });
  };
  
  // Calculate what percent one value is of another (e.g., 20 is what percent of 100? = 20%)
  const calculateValueIsPercent = (e) => {
    e.preventDefault();
    const { value, percent } = valueIsPercent;
    if (!value || !percent) return;
    
    const result = (parseFloat(value) / parseFloat(percent)) * 100;
    setValueIsPercent({ ...valueIsPercent, result: result.toFixed(2) });
  };
  
  // Calculate percent change between two values (e.g., 100 to 120 = 20% increase)
  const calculatePercentChange = (e) => {
    e.preventDefault();
    const { oldValue, newValue } = percentChange;
    if (!oldValue || !newValue) return;
    
    const oldVal = parseFloat(oldValue);
    const newVal = parseFloat(newValue);
    const change = newVal - oldVal;
    const percentChangeValue = (change / Math.abs(oldVal)) * 100;
    
    setPercentChange({ 
      ...percentChange, 
      result: {
        percentChange: percentChangeValue.toFixed(2),
        isIncrease: change >= 0
      }
    });
  };
  
  // Calculate final price after discount (e.g., $100 with 20% discount = $80)
  const calculateDiscount = (e) => {
    e.preventDefault();
    const { originalPrice, discountPercent } = discount;
    if (!originalPrice || !discountPercent) return;
    
    const original = parseFloat(originalPrice);
    const discountAmount = original * (parseFloat(discountPercent) / 100);
    const finalPrice = original - discountAmount;
    
    setDiscount({ 
      ...discount, 
      result: {
        discountAmount: discountAmount.toFixed(2),
        finalPrice: finalPrice.toFixed(2)
      }
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Percentage Calculator - Calculate Percentages, Discounts & Changes | ToollyHub"
        description="Calculate percentages, discounts, increases, and decreases with our comprehensive percentage calculator. Perfect for shopping, budgeting, business calculations, and financial analysis. Get instant results for percentage of value, value as percentage, percentage change, and discount calculations."
        keywords="percentage calculator, percent calculator, discount calculator, percentage increase, percentage decrease, calculate percentages, percent off calculator, percentage change calculator, percentage of value, value as percentage, discount percentage, percentage tool, percent math, percentage formula, business calculator, financial calculator, shopping calculator, free percentage calculator, online percent calculator"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Percentage Calculator",
          "description": "Calculate percentages, discounts, increases, and decreases for various needs",
          "url": "https://toollyhub.com/percentage-calculator",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Percentage of value calculation",
            "Value as percentage calculation",
            "Percentage change calculation",
            "Discount calculation",
            "Instant results",
            "Multiple calculation types",
            "Business and shopping applications",
            "Financial analysis support"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Percentage Calculator' },
          { property: 'og:image:alt', content: 'Percentage Calculator - Calculate Percentages, Discounts & Changes' }
        ]}
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Percentage Calculator</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Calculate percentages, discounts, and changes with ease. Perfect for shopping, finance, and everyday calculations.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Calculate X% of Y */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
              <h2 className="text-2xl font-bold text-white">Percentage of a Value</h2>
              <p className="text-blue-100 mt-1">Calculate what percentage of a number is</p>
            </div>
            <div className="p-6">
              <form onSubmit={calculatePercentOfValue} className="space-y-6">
                <div className="space-y-4">
                  <div className="flex flex-wrap items-center gap-3 bg-gray-50 p-4 rounded-xl">
                    <input
                      type="number"
                      id="percent1"
                      value={percentOfValue.percent}
                      onChange={(e) => setPercentOfValue({...percentOfValue, percent: e.target.value})}
                      className="w-24 sm:w-28 px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
                      placeholder="20"
                      step="any"
                      required
                    />
                    <span className="text-xl font-medium text-gray-700">% of</span>
                    <input
                      type="number"
                      value={percentOfValue.value}
                      onChange={(e) => setPercentOfValue({...percentOfValue, value: e.target.value})}
                      className="w-24 sm:w-28 px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
                      placeholder="100"
                      step="any"
                      required
                    />
                    <span className="text-xl font-medium text-gray-700">=</span>
                    <div className="flex-grow min-w-[100px] px-4 py-3 bg-gray-100 rounded-lg text-right font-medium">
                      {percentOfValue.result !== null ? percentOfValue.result : '?'}
                    </div>
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                >
                  Calculate
                </button>
              </form>
            </div>
          </div>

          {/* Calculate X is what percent of Y */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
              <h2 className="text-2xl font-bold text-white">Value as a Percentage</h2>
              <p className="text-green-100 mt-1">Find what percentage one number is of another</p>
            </div>
            <div className="p-6">
              <form onSubmit={calculateValueIsPercent} className="space-y-6">
                <div className="space-y-4">
                  <div className="flex flex-wrap items-center gap-3 bg-gray-50 p-4 rounded-xl">
                    <input
                      type="number"
                      id="value2"
                      value={valueIsPercent.value}
                      onChange={(e) => setValueIsPercent({...valueIsPercent, value: e.target.value})}
                      className="w-24 sm:w-28 px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition"
                      placeholder="20"
                      step="any"
                      required
                    />
                    <span className="text-xl font-medium text-gray-700">is what % of</span>
                    <input
                      type="number"
                      value={valueIsPercent.percent}
                      onChange={(e) => setValueIsPercent({...valueIsPercent, percent: e.target.value})}
                      className="w-24 sm:w-28 px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition"
                      placeholder="100"
                      step="any"
                      required
                    />
                    <span className="text-xl font-medium text-gray-700">=</span>
                    <div className="flex-grow min-w-[100px] px-4 py-3 bg-gray-100 rounded-lg text-right font-medium">
                      {valueIsPercent.result !== null ? `${valueIsPercent.result}%` : '?'}
                    </div>
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                >
                  Calculate
                </button>
              </form>
            </div>
          </div>

          {/* Calculate percent change */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
              <h2 className="text-2xl font-bold text-white">Percentage Change</h2>
              <p className="text-purple-100 mt-1">Calculate increase or decrease between two values</p>
            </div>
            <div className="p-6">
              <form onSubmit={calculatePercentChange} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="oldValue" className="block text-sm font-medium text-gray-700">
                      Original Value
                    </label>
                    <input
                      type="number"
                      id="oldValue"
                      value={percentChange.oldValue}
                      onChange={(e) => setPercentChange({...percentChange, oldValue: e.target.value})}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition"
                      placeholder="100"
                      step="any"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="newValue" className="block text-sm font-medium text-gray-700">
                      New Value
                    </label>
                    <input
                      type="number"
                      id="newValue"
                      value={percentChange.newValue}
                      onChange={(e) => setPercentChange({...percentChange, newValue: e.target.value})}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition"
                      placeholder="120"
                      step="any"
                      required
                    />
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                >
                  Calculate Change
                </button>
                
                {percentChange.result && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium text-gray-700">Change:</span>
                      <span className={`text-xl font-bold ${percentChange.result.isIncrease ? 'text-green-600' : 'text-red-600'}`}>
                        {Math.abs(percentChange.result.percentChange)}% {percentChange.result.isIncrease ? '↑' : '↓'}
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      From {percentChange.oldValue} to {percentChange.newValue}
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>

          {/* Calculate discount */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6">
              <h2 className="text-2xl font-bold text-white">Discount Calculator</h2>
              <p className="text-orange-100 mt-1">Calculate final price after discount</p>
            </div>
            <div className="p-6">
              <form onSubmit={calculateDiscount} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="originalPrice" className="block text-sm font-medium text-gray-700">
                      Original Price
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500">$</span>
                      </div>
                      <input
                        type="number"
                        id="originalPrice"
                        value={discount.originalPrice}
                        onChange={(e) => setDiscount({...discount, originalPrice: e.target.value})}
                        className="w-full pl-7 px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition"
                        placeholder="100.00"
                        step="any"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="discountPercent" className="block text-sm font-medium text-gray-700">
                      Discount Percentage
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="discountPercent"
                        value={discount.discountPercent}
                        onChange={(e) => setDiscount({...discount, discountPercent: e.target.value})}
                        className="w-full pr-8 px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition"
                        placeholder="20"
                        step="any"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500">%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold rounded-lg hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                >
                  Calculate Discount
                </button>
                
                {discount.result && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-100">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center text-gray-700">
                        <span>Original Price:</span>
                        <span className="font-medium">${discount.originalPrice}</span>
                      </div>
                      <div className="flex justify-between items-center text-gray-700">
                        <span>Discount ({discount.discountPercent}%):</span>
                        <span className="font-medium text-red-600">-${discount.result.discountAmount}</span>
                      </div>
                      <div className="pt-3 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                          <span className="font-semibold text-gray-800">Final Price:</span>
                          <span className="text-xl font-bold text-green-600">${discount.result.finalPrice}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Understanding Percentages</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">What is a Percentage?</h3>
              <p className="text-gray-600 leading-relaxed">
                A percentage is a number expressed as a fraction of 100. The symbol "%" represents percentage. 
                For example, 50% means 50 out of 100, or half of the total.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Common Applications</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Shopping discounts and sales
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  Financial calculations and interest rates
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Business growth and profit margins
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Academic grades and test scores
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PercentageCalculator;
