name: Deploy React + Vite App

on:
  push:
    branches: [ feature/initial-setup-and-ui ]
  pull_request:
    branches: [ feature/initial-setup-and-ui ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.9.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
        
    - name: Add server to known hosts
      run: |
        mkdir -p ~/.ssh
        ssh-keyscan -p ${{ secrets.SSH_PORT }} ${{ secrets.SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Create backup of current deployment
      run: |
        ssh -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SERVER_IP }} << 'EOF'
          # Create backup directory with timestamp
          BACKUP_DIR="${{ secrets.BACKUP_PATH }}/backup-$(date +%Y%m%d-%H%M%S)"
          mkdir -p "$BACKUP_DIR"
          
          # Backup current deployment if it exists
          if [ -d "${{ secrets.DEPLOY_PATH }}" ]; then
            echo "Creating backup of current deployment..."
            cp -r "${{ secrets.DEPLOY_PATH }}"/* "$BACKUP_DIR/" 2>/dev/null || true
            echo "Backup created at: $BACKUP_DIR"
          else
            echo "No existing deployment to backup"
          fi
          
          # Keep only last 5 backups
          # find /var/backups/react-app -type d -name "backup-*" | sort -r | tail -n +6 | xargs rm -rf
        EOF
        
    - name: Deploy to server
      run: |
        # Create deployment directory if it doesn't exist
        ssh -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SERVER_IP }} "mkdir -p ${{ secrets.DEPLOY_PATH }}"
        
        # Clear existing files
        ssh -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SERVER_IP }} "rm -rf ${{ secrets.DEPLOY_PATH }}/*"
        
        # Upload new build files
        scp -P ${{ secrets.SSH_PORT }} -r dist/* ${{ secrets.SSH_USER }}@${{ secrets.SERVER_IP }}:~/temp-deploy/
        
        # Move files to deployment directory with proper permissions
        ssh -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SERVER_IP }} << 'EOF'
          mkdir -p ~/temp-deploy
          mv ~/temp-deploy/* "${{ secrets.DEPLOY_PATH }}/"
          chown -R www-data:www-data "${{ secrets.DEPLOY_PATH }}"
          chmod -R 755 "${{ secrets.DEPLOY_PATH }}"
          rm -rf ~/temp-deploy
        EOF
        
        
    - name: Notify deployment status
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ Deployment successful!"
        else
          echo "❌ Deployment failed!"
        fi