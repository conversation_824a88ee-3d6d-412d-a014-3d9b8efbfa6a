import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const TextCounter = () => {
  const [text, setText] = useState('');
  const [stats, setStats] = useState({
    characters: 0,
    charactersNoSpaces: 0,
    words: 0,
    sentences: 0,
    paragraphs: 0,
    lines: 0
  });

  // Update stats whenever text changes
  useEffect(() => {
    // Character count (with spaces)
    const characters = text.length;
    
    // Character count (without spaces)
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    
    // Word count
    const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
    
    // Sentence count (approximation)
    const sentences = text === '' ? 0 : text.split(/[.!?]+/).filter(Boolean).length;
    
    // Paragraph count
    const paragraphs = text === '' ? 0 : text.split(/\n+/).filter(p => p.trim() !== '').length;
    
    // Line count
    const lines = text === '' ? 0 : text.split('\n').length;
    
    setStats({
      characters,
      charactersNoSpaces,
      words,
      sentences,
      paragraphs,
      lines
    });
  }, [text]);

  const handleClearText = () => {
    setText('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Text Counter - Count Characters, Words, Sentences & More | ToollyHub"
        description="Free online text counter tool to count characters, words, sentences, paragraphs, and lines in your text. Get detailed text statistics and analysis. Perfect for writers, students, social media users, content creators, and SEO professionals. Real-time counting with character limits."
        keywords="character counter, word counter, text counter, sentence counter, paragraph counter, line counter, text statistics, text analysis, character count, word count, writing tool, content analysis, social media counter, SEO text counter, text metrics, writing statistics, content counter, text length, character limit, word limit, free text counter, online text counter"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Text Counter",
          "description": "Count characters, words, sentences, paragraphs, and lines in text",
          "url": "https://toollyhub.com/text-counter",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Character counting",
            "Word counting",
            "Sentence counting",
            "Paragraph counting",
            "Line counting",
            "Real-time statistics",
            "Text analysis",
            "Character limit checking"
          ],
          "audience": {
            "@type": "Audience",
            "audienceType": ["Writers", "Students", "Content Creators", "SEO Professionals", "Social Media Users"]
          }
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Text Counter' },
          { property: 'og:image:alt', content: 'Text Counter - Count Characters, Words, Sentences & More' }
        ]}
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Text Counter</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Count characters, words, sentences and more in your text with this easy-to-use tool.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
                <h2 className="text-2xl font-bold text-white">Enter Your Text</h2>
                <p className="text-indigo-100 mt-1">Type or paste your text below to analyze</p>
              </div>
              
              <div className="p-6">
                <div className="flex justify-end mb-2">
                  <button
                    onClick={handleClearText}
                    className="px-4 py-1 text-sm text-indigo-600 bg-indigo-50 hover:bg-indigo-100 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors"
                    disabled={text === ''}
                  >
                    Clear Text
                  </button>
                </div>
                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  className="w-full h-96 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                  placeholder="Type or paste your text here..."
                />
              </div>
            </div>
          </div>
          
          <div>
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl h-full">
              <div className="bg-gradient-to-r from-cyan-500 to-cyan-600 p-6">
                <h2 className="text-2xl font-bold text-white">Text Statistics</h2>
                <p className="text-cyan-100 mt-1">Real-time analysis of your content</p>
              </div>
              
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl border border-gray-100 transition-all hover:border-cyan-200 hover:bg-cyan-50">
                    <span className="font-medium text-gray-700">Characters (with spaces)</span>
                    <span className="text-lg font-semibold text-cyan-600">{stats.characters}</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl border border-gray-100 transition-all hover:border-cyan-200 hover:bg-cyan-50">
                    <span className="font-medium text-gray-700">Characters (no spaces)</span>
                    <span className="text-lg font-semibold text-cyan-600">{stats.charactersNoSpaces}</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl border border-gray-100 transition-all hover:border-cyan-200 hover:bg-cyan-50">
                    <span className="font-medium text-gray-700">Words</span>
                    <span className="text-lg font-semibold text-cyan-600">{stats.words}</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl border border-gray-100 transition-all hover:border-cyan-200 hover:bg-cyan-50">
                    <span className="font-medium text-gray-700">Sentences</span>
                    <span className="text-lg font-semibold text-cyan-600">{stats.sentences}</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl border border-gray-100 transition-all hover:border-cyan-200 hover:bg-cyan-50">
                    <span className="font-medium text-gray-700">Paragraphs</span>
                    <span className="text-lg font-semibold text-cyan-600">{stats.paragraphs}</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl border border-gray-100 transition-all hover:border-cyan-200 hover:bg-cyan-50">
                    <span className="font-medium text-gray-700">Lines</span>
                    <span className="text-lg font-semibold text-cyan-600">{stats.lines}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Use a Text Counter?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Use Cases & Benefits</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-2">•</span>
                  <strong>For Writers:</strong> Track word count goals for articles and books
                </li>
                <li className="flex items-center">
                  <span className="text-cyan-500 mr-2">•</span>
                  <strong>For Students:</strong> Meet word count requirements for essays
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  <strong>For Social Media:</strong> Stay within character limits
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  <strong>For SEO:</strong> Optimize content length for search engines
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  <strong>For Translators:</strong> Estimate workload based on counts
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">What Our Tool Provides</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <div>
                    <strong>Character Counts:</strong> Total characters with and without spaces
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-cyan-500 mr-2">•</span>
                  <div>
                    <strong>Word Count:</strong> Total number of words in your text
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  <div>
                    <strong>Sentence Count:</strong> Based on punctuation marks (., !, ?)
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <div>
                    <strong>Paragraph Count:</strong> Number of text blocks separated by blank lines
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <div>
                    <strong>Line Count:</strong> Number of line breaks in your content
                  </div>
                </li>
              </ul>
            </div>
          </div>
          
          <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">How to Use the Text Counter</h3>
          <ol className="space-y-2 text-gray-600">
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">1.</span>
              <span>Type or paste your text into the text area on the left</span>
            </li>
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">2.</span>
              <span>View real-time statistics in the panel on the right</span>
            </li>
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">3.</span>
              <span>Use the "Clear Text" button to start fresh when needed</span>
            </li>
          </ol>
          
          <div className="bg-indigo-50 p-6 rounded-xl mt-6">
            <h3 className="text-lg font-semibold text-indigo-700 mb-2">Privacy & Security</h3>
            <p className="text-gray-700">
              Your text is processed entirely in your browser. We don't store, transmit, or analyze your content on our servers, ensuring complete privacy for sensitive information.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextCounter;
