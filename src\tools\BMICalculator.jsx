import React, { useState } from 'react';
import SEO from '../components/SEO';

const BMICalculator = () => {
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [unit, setUnit] = useState('metric'); // 'metric' or 'imperial'
  const [bmi, setBmi] = useState(null);
  const [bmiCategory, setBmiCategory] = useState('');
  const [error, setError] = useState('');

  const calculateBMI = () => {
    if (!weight || !height) {
      setError('Please enter both weight and height');
      setBmi(null);
      setBmiCategory('');
      return;
    }

    if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
      setError('Please enter valid values');
      setBmi(null);
      setBmiCategory('');
      return;
    }

    setError('');
    
    let bmiValue;
    if (unit === 'metric') {
      // Weight in kg, height in cm
      bmiValue = (weight / Math.pow(height / 100, 2)).toFixed(2);
    } else {
      // Weight in lbs, height in inches
      bmiValue = ((weight * 703) / Math.pow(height, 2)).toFixed(2);
    }
    
    setBmi(parseFloat(bmiValue));
    setBmiCategory(getBMICategory(parseFloat(bmiValue)));
  };

  const getBMICategory = (bmi) => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi >= 18.5 && bmi < 25) return 'Normal weight';
    if (bmi >= 25 && bmi < 30) return 'Overweight';
    if (bmi >= 30 && bmi < 35) return 'Obesity Class I';
    if (bmi >= 35 && bmi < 40) return 'Obesity Class II';
    return 'Obesity Class III';
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Underweight': return 'text-blue-600';
      case 'Normal weight': return 'text-green-600';
      case 'Overweight': return 'text-yellow-600';
      case 'Obesity Class I': return 'text-orange-600';
      case 'Obesity Class II': return 'text-red-500';
      case 'Obesity Class III': return 'text-red-700';
      default: return 'text-gray-800';
    }
  };

  const handleUnitChange = (e) => {
    setUnit(e.target.value);
    setWeight('');
    setHeight('');
    setBmi(null);
    setBmiCategory('');
    setError('');
  };

  return (
    <>
      <SEO
        title="BMI Calculator - Free Body Mass Index Calculator with Categories | ToollyHub"
        description="Free BMI calculator to determine your Body Mass Index with instant results. Calculate BMI using metric (kg/cm) or imperial (lbs/in) units. Get your weight category (underweight, normal, overweight, obese) and learn about healthy BMI ranges. Simple, accurate, and medically reliable."
        keywords="BMI calculator, body mass index, weight calculator, health calculator, BMI formula, weight category, healthy weight, obesity calculator, underweight calculator, overweight calculator, BMI chart, BMI categories, BMI ranges, health assessment, weight status, fitness calculator, medical calculator, BMI metric, BMI imperial, body weight index, weight management, health screening, BMI tool online, free BMI calculator"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "BMI Calculator",
          "description": "Free BMI calculator to determine your Body Mass Index with weight categories",
          "url": "https://toollyhub.com/bmi-calculator",
          "applicationCategory": "HealthApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "BMI calculation with metric units (kg/cm)",
            "BMI calculation with imperial units (lbs/in)",
            "Weight category classification",
            "BMI ranges and categories",
            "Health assessment guidance",
            "Instant results",
            "Medical accuracy"
          ],
          "medicalSpecialty": "General Medicine"
        }}
        additionalMeta={[
          { name: 'application-name', content: 'BMI Calculator' },
          { property: 'og:image:alt', content: 'BMI Calculator - Free Body Mass Index Calculator with Categories' },
          { name: 'medical-disclaimer', content: 'This BMI calculator is for informational purposes only and should not replace professional medical advice.' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
            <h2 className="text-2xl font-bold text-white">BMI Calculator</h2>
            <p className="text-green-100 mt-1">Calculate your Body Mass Index</p>
          </div>

          <div className="p-6 space-y-6">
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">Unit System</label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    value="metric"
                    checked={unit === 'metric'}
                    onChange={handleUnitChange}
                    className="form-radio h-4 w-4 text-green-600"
                  />
                  <span className="ml-2">Metric (kg, cm)</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    value="imperial"
                    checked={unit === 'imperial'}
                    onChange={handleUnitChange}
                    className="form-radio h-4 w-4 text-green-600"
                  />
                  <span className="ml-2">Imperial (lbs, in)</span>
                </label>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
                  Weight ({unit === 'metric' ? 'kg' : 'lbs'})
                </label>
                <input
                  type="number"
                  id="weight"
                  value={weight}
                  onChange={(e) => setWeight(e.target.value)}
                  className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition"
                  placeholder={`Enter weight in ${unit === 'metric' ? 'kilograms' : 'pounds'}`}
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="height" className="block text-sm font-medium text-gray-700">
                  Height ({unit === 'metric' ? 'cm' : 'in'})
                </label>
                <input
                  type="number"
                  id="height"
                  value={height}
                  onChange={(e) => setHeight(e.target.value)}
                  className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition"
                  placeholder={`Enter height in ${unit === 'metric' ? 'centimeters' : 'inches'}`}
                  required
                />
              </div>
            </div>

            {error && (
              <div className="p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg">
                {error}
              </div>
            )}

            <button
              onClick={calculateBMI}
              className="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
            >
              Calculate BMI
            </button>

            {bmi !== null && (
              <div className="space-y-6">
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">Your BMI Result</h3>
                  <div className="text-center mb-4">
                    <p className="text-3xl font-bold text-green-600 mb-2">{bmi}</p>
                    <p className={`text-lg font-medium ${getCategoryColor(bmiCategory)}`}>
                      {bmiCategory}
                    </p>
                  </div>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">BMI Categories</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between items-center text-gray-700">
                      <span className="text-blue-600 font-medium">Underweight:</span>
                      <span>&lt; 18.5</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span className="text-green-600 font-medium">Normal weight:</span>
                      <span>18.5 - 24.9</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span className="text-yellow-600 font-medium">Overweight:</span>
                      <span>25 - 29.9</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span className="text-orange-600 font-medium">Obesity Class I:</span>
                      <span>30 - 34.9</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span className="text-red-500 font-medium">Obesity Class II:</span>
                      <span>35 - 39.9</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span className="text-red-700 font-medium">Obesity Class III:</span>
                      <span>≥ 40</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default BMICalculator;
