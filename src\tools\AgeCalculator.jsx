import React, { useState } from 'react';
import SEO from '../components/SEO';

const AgeCalculator = () => {
  const [birthDate, setBirthDate] = useState('');
  const [age, setAge] = useState(null);
  const [error, setError] = useState('');

  const calculateAge = () => {
    if (!birthDate) {
      setError('Please select a birth date');
      setAge(null);
      return;
    }

    setError('');

    const today = new Date();
    const birthDateObj = new Date(birthDate);

    if (birthDateObj > today) {
      setError('Birth date cannot be in the future');
      setAge(null);
      return;
    }

    let ageYears = today.getFullYear() - birthDateObj.getFullYear();
    let ageMonths = today.getMonth() - birthDateObj.getMonth();
    let ageDays = today.getDate() - birthDateObj.getDate();

    if (ageDays < 0) {
      ageMonths--;
      // Get the last day of the previous month
      const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0).getDate();
      ageDays += lastDayOfLastMonth;
    }

    if (ageMonths < 0) {
      ageYears--;
      ageMonths += 12;
    }

    // Calculate additional details
    const totalDays = Math.floor((today - birthDateObj) / (1000 * 60 * 60 * 24));
    const totalWeeks = Math.floor(totalDays / 7);
    const totalMonths = ageYears * 12 + ageMonths;
    const totalHours = totalDays * 24;
    const totalMinutes = totalHours * 60;

    setAge({
      years: ageYears,
      months: ageMonths,
      days: ageDays,
      totalDays,
      totalWeeks,
      totalMonths,
      totalHours,
      totalMinutes
    });
  };

  const handleDateChange = (e) => {
    setBirthDate(e.target.value);
    setError('');
    setAge(null);
  };

  return (
    <>
      <SEO
        title="Age Calculator - Calculate Your Exact Age in Years, Months & Days | ToollyHub"
        description="Calculate your exact age in years, months, and days with our free age calculator. Find out your precise age, total days lived, weeks, hours, and minutes. Perfect for birthdays, age verification, life milestones, and age differences. Simple, accurate, and easy to use."
        keywords="age calculator, birthday calculator, age difference calculator, life calculator, date calculator, age in years months days, age calculation tool, exact age calculator, how old am i, age finder, birthday countdown, age verification, life milestones, age tracker, precise age, total days lived, age in days, age in hours, age in minutes, free age calculator, online age calculator"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Age Calculator",
          "description": "Calculate your exact age in years, months, and days",
          "url": "https://toollyhub.com/age-calculator",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Calculate exact age in years, months, and days",
            "Total days lived calculation",
            "Total weeks calculation",
            "Total months calculation",
            "Total hours calculation",
            "Total minutes calculation",
            "Age verification",
            "Birthday tracking"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Age Calculator' },
          { property: 'og:image:alt', content: 'Age Calculator - Calculate Your Exact Age in Years, Months & Days' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
            <h2 className="text-2xl font-bold text-white">Age Calculator</h2>
            <p className="text-purple-100 mt-1">Calculate your exact age with precision</p>
          </div>

          <div className="p-6 space-y-6">
            <div className="space-y-4">
              <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700">
                Select Your Birth Date
              </label>
              <input
                type="date"
                id="birthDate"
                value={birthDate}
                onChange={handleDateChange}
                className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition"
                required
              />
            </div>

            {error && (
              <div className="p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg">
                {error}
              </div>
            )}

            <button
              onClick={calculateAge}
              className="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
            >
              Calculate Age
            </button>

            {age && (
              <div className="space-y-6">
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">Your Age</h3>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-white p-3 rounded-lg border border-gray-200">
                      <span className="block text-2xl font-bold text-purple-600">{age.years}</span>
                      <span className="text-sm text-gray-600">Years</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-gray-200">
                      <span className="block text-2xl font-bold text-purple-600">{age.months}</span>
                      <span className="text-sm text-gray-600">Months</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-gray-200">
                      <span className="block text-2xl font-bold text-purple-600">{age.days}</span>
                      <span className="text-sm text-gray-600">Days</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Additional Details</h4>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex justify-between items-center text-gray-700">
                      <span>Total Days:</span>
                      <span className="font-medium text-blue-600">{age.totalDays?.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span>Total Weeks:</span>
                      <span className="font-medium text-green-600">{age.totalWeeks?.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span>Total Months:</span>
                      <span className="font-medium text-orange-600">{age.totalMonths}</span>
                    </div>
                    <div className="flex justify-between items-center text-gray-700">
                      <span>Total Hours:</span>
                      <span className="font-medium text-red-600">{age.totalHours?.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AgeCalculator;
