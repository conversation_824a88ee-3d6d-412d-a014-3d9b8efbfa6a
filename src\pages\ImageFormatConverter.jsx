import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const ImageFormatConverter = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [originalFormat, setOriginalFormat] = useState('');
  const [targetFormat, setTargetFormat] = useState('jpeg');
  const [quality, setQuality] = useState(90);
  const [convertedImage, setConvertedImage] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [originalSize, setOriginalSize] = useState(0);
  const [convertedSize, setConvertedSize] = useState(0);
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  // Supported formats
  const formats = [
    { value: 'jpeg', label: 'JPEG', mimeType: 'image/jpeg', extension: '.jpg' },
    { value: 'png', label: 'PNG', mimeType: 'image/png', extension: '.png' },
    { value: 'webp', label: 'WebP', mimeType: 'image/webp', extension: '.webp' },
    { value: 'gif', label: 'GIF', mimeType: 'image/gif', extension: '.gif' },
    { value: 'bmp', label: 'BMP', mimeType: 'image/bmp', extension: '.bmp' },
  ];

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      setOriginalSize(file.size);
      
      // Detect original format
      const fileType = file.type.split('/')[1];
      setOriginalFormat(fileType === 'jpeg' ? 'jpg' : fileType);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
      setConvertedImage(null);
      setConvertedSize(0);
    } else {
      alert('Please select a valid image file.');
    }
  };

  // Convert image format
  const convertImage = () => {
    if (!selectedImage || !imagePreview) return;

    setIsProcessing(true);
    
    const img = new Image();
    img.onload = () => {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      canvas.width = img.width;
      canvas.height = img.height;
      
      // For formats that don't support transparency, fill with white background
      if (targetFormat === 'jpeg' || targetFormat === 'bmp') {
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }
      
      // Draw image
      ctx.drawImage(img, 0, 0);
      
      // Get target format info
      const targetFormatInfo = formats.find(f => f.value === targetFormat);
      const qualityValue = targetFormat === 'jpeg' || targetFormat === 'webp' ? quality / 100 : 1;
      
      // Convert to blob
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        setConvertedImage(url);
        setConvertedSize(blob.size);
        setIsProcessing(false);
      }, targetFormatInfo.mimeType, qualityValue);
    };
    
    img.src = imagePreview;
  };

  // Download converted image
  const downloadImage = () => {
    if (!convertedImage) return;
    
    const targetFormatInfo = formats.find(f => f.value === targetFormat);
    const originalName = selectedImage.name.split('.')[0];
    
    const link = document.createElement('a');
    link.href = convertedImage;
    link.download = `${originalName}_converted${targetFormatInfo.extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Reset all states
  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setConvertedImage(null);
    setOriginalFormat('');
    setTargetFormat('jpeg');
    setQuality(90);
    setOriginalSize(0);
    setConvertedSize(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Calculate compression ratio
  const getCompressionRatio = () => {
    if (originalSize === 0 || convertedSize === 0) return null;
    const ratio = ((originalSize - convertedSize) / originalSize * 100);
    return ratio > 0 ? `${ratio.toFixed(1)}% smaller` : `${Math.abs(ratio).toFixed(1)}% larger`;
  };

  return (
    <>
      <SEO
        title="Image Format Converter - Convert JPEG, PNG, WebP, GIF Online Free | ToollyHub"
        description="Convert images between different formats including JPEG, PNG, WebP, GIF, and BMP. Adjust quality settings and download converted images instantly. Perfect for web optimization, compatibility, and file size reduction. Support for batch conversion and quality control."
        keywords="image format converter, convert image format, JPEG to PNG, PNG to WebP, image conversion, format conversion, image converter online, JPEG converter, PNG converter, WebP converter, GIF converter, BMP converter, image file converter, photo format converter, picture converter, web image optimization, image compatibility, file format changer, free image converter, online format converter"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Image Format Converter",
          "description": "Convert images between different formats with quality control",
          "url": "https://toollyhub.com/image-format-converter",
          "applicationCategory": "MultimediaApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "JPEG format conversion",
            "PNG format conversion",
            "WebP format conversion",
            "GIF format conversion",
            "BMP format conversion",
            "Quality adjustment",
            "Instant download",
            "Preview before conversion"
          ]
        }}
        additionalMeta={[
          { name: 'application-name', content: 'Image Format Converter' },
          { property: 'og:image:alt', content: 'Image Format Converter - Convert JPEG, PNG, WebP, GIF Online Free' }
        ]}
      />
      <div className="container mx-auto p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">Image Format Converter</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Convert between JPEG, PNG, WebP, GIF, and more
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Controls Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-6">
                <h2 className="text-xl font-bold text-white">Upload & Convert</h2>
                <p className="text-purple-100 mt-1">Select image and target format</p>
              </div>
              <div className="p-6 space-y-6">
                
                {/* File Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="imageInput"
                    />
                    <label htmlFor="imageInput" className="cursor-pointer">
                      <div className="space-y-2">
                        <div className="text-4xl">📁</div>
                        <p className="text-gray-600">Click to select an image</p>
                        <p className="text-sm text-gray-400">Supports JPG, PNG, GIF, WebP, BMP</p>
                      </div>
                    </label>
                  </div>
                </div>

                {selectedImage && (
                  <>
                    {/* Original Image Info */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-medium text-gray-800 mb-2">Original Image</h3>
                      <p className="text-sm text-gray-600">
                        <strong>File:</strong> {selectedImage.name}
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Format:</strong> {originalFormat.toUpperCase()}
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Size:</strong> {formatFileSize(originalSize)}
                      </p>
                    </div>

                    {/* Target Format Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Convert To</label>
                      <div className="grid grid-cols-2 gap-3">
                        {formats.map((format) => (
                          <button
                            key={format.value}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                              targetFormat === format.value
                                ? 'border-purple-500 bg-purple-50 text-purple-700'
                                : 'border-gray-200 hover:border-purple-300 text-gray-700'
                            }`}
                            onClick={() => setTargetFormat(format.value)}
                          >
                            <div className="font-medium">{format.label}</div>
                            <div className="text-xs text-gray-500">{format.extension}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Quality Settings (for JPEG and WebP) */}
                    {(targetFormat === 'jpeg' || targetFormat === 'webp') && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quality: {quality}%
                        </label>
                        <input
                          type="range"
                          min="10"
                          max="100"
                          value={quality}
                          onChange={(e) => setQuality(parseInt(e.target.value))}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>Lower size</span>
                          <span>Higher quality</span>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={convertImage}
                        disabled={isProcessing}
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? 'Converting...' : 'Convert Image'}
                      </button>
                      <button
                        onClick={resetTool}
                        className="px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md"
                      >
                        Reset
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Preview Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6">
                <h2 className="text-xl font-bold text-white">Preview & Download</h2>
                <p className="text-orange-100 mt-1">Original and converted image preview</p>
              </div>
              <div className="p-6">
                {imagePreview ? (
                  <div className="space-y-6">
                    {/* Original Image */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-3">Original Image</h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <img
                          src={imagePreview}
                          alt="Original"
                          className="max-w-full h-auto max-h-48 mx-auto rounded-lg shadow-sm"
                        />
                      </div>
                    </div>

                    {/* Converted Image */}
                    {convertedImage && (
                      <div>
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="text-lg font-semibold text-gray-800">Converted Image</h3>
                          <button
                            onClick={downloadImage}
                            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-md text-sm"
                          >
                            📥 Download
                          </button>
                        </div>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 mb-4">
                          <img
                            src={convertedImage}
                            alt="Converted"
                            className="max-w-full h-auto max-h-48 mx-auto rounded-lg shadow-sm"
                          />
                        </div>
                        
                        {/* Conversion Stats */}
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-2">Conversion Results</h4>
                          <div className="space-y-1 text-sm text-gray-600">
                            <p><strong>Format:</strong> {originalFormat.toUpperCase()} → {targetFormat.toUpperCase()}</p>
                            <p><strong>Original Size:</strong> {formatFileSize(originalSize)}</p>
                            <p><strong>New Size:</strong> {formatFileSize(convertedSize)}</p>
                            {getCompressionRatio() && (
                              <p><strong>Size Change:</strong> {getCompressionRatio()}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🔄</div>
                    <p className="text-gray-500 text-lg">Upload an image to convert format</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Hidden canvas for image processing */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">Understanding Image Formats</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Popular Image Formats</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Different image formats serve different purposes. Our converter supports all major formats,
                  allowing you to choose the best format for your specific needs.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <strong>JPEG:</strong> Best for photos with many colors
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    <strong>PNG:</strong> Perfect for images with transparency
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    <strong>WebP:</strong> Modern format with excellent compression
                  </li>
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    <strong>GIF:</strong> Ideal for simple animations
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Conversion Benefits</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    Optimize file sizes for web use
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    Ensure compatibility across platforms
                  </li>
                  <li className="flex items-center">
                    <span className="text-teal-500 mr-2">•</span>
                    Maintain image quality during conversion
                  </li>
                  <li className="flex items-center">
                    <span className="text-pink-500 mr-2">•</span>
                    Adjustable quality settings for optimal results
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageFormatConverter;
